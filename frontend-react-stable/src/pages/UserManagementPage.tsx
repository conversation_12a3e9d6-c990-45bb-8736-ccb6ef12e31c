import React, { useState, useEffect, useCallback } from 'react';
import {
  Typography,
  Card,
  Alert,
  Form,
  Input,
  Button,
  message,
  Space,
  Table,
  Tag,
  Tabs,
} from 'antd';
import {
  LockOutlined,
  UserAddOutlined,
  UserOutlined,
  EyeInvisibleOutlined,
  EyeTwoTone,
  SafetyOutlined,
  ExclamationCircleOutlined,
  SettingOutlined,
  KeyOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import { useSelector } from 'react-redux';
import { RootState } from '../store/store';
import { authAPI } from '../services/api';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
// const { confirm } = Modal;

interface User {
  username: string;
  is_admin: boolean;
}

const UserManagementPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [users, setUsers] = useState<User[]>([]);
  const [changePasswordForm] = Form.useForm();
  const [addUserForm] = Form.useForm();

  // 从Redux store获取当前用户信息
  const { user, token } = useSelector((state: RootState) => state.auth);
  const currentUser = user?.username || '';
  const isAdmin = currentUser === 'admin';

  // 获取用户列表（仅管理员）
  const fetchUsers = useCallback(async () => {
    if (!token) return;

    try {
      const response = await authAPI.getUsers(token);
      if (response.data) {
        // 将用户对象转换为数组格式
        const userList = Object.entries(response.data).map(([username]: [string, any]) => ({
          username,
          is_admin: username === 'admin',
        }));
        setUsers(userList);
      }
    } catch (error: any) {
      console.error('获取用户列表失败:', error);
      message.error(`❌ 获取用户列表失败: ${error.response?.data?.detail || error.message}`);
    }
  }, [token]);

  // 获取当前用户信息
  useEffect(() => {
    if (isAdmin && token) {
      fetchUsers();
    }
  }, [isAdmin, token, fetchUsers]);

  // 修改密码
  const handleChangePassword = async (values: any) => {
    if (!token) return;

    setLoading(true);
    try {
      const response = await authAPI.changePassword({
        username: currentUser,
        old_password: values.old_password,
        new_password: values.new_password,
        confirm_password: values.confirm_password
      }, token);

      if (response.data.message) {
        message.success('✅ 密码修改成功');
        changePasswordForm.resetFields();
      }
    } catch (error: any) {
      console.error('修改密码失败:', error);
      message.error(`❌ 修改密码失败: ${error.response?.data?.detail || error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // 添加用户（仅管理员）
  const handleAddUser = async (values: any) => {
    if (!token) return;

    setLoading(true);
    try {
      const response = await authAPI.addUser({
        username: currentUser,
        new_username: values.new_username,
        new_user_password: values.new_user_password,
        confirm_user_password: values.confirm_user_password
      }, token);

      if (response.data.message) {
        message.success('✅ 用户添加成功');
        addUserForm.resetFields();
        fetchUsers(); // 刷新用户列表
      }
    } catch (error: any) {
      console.error('添加用户失败:', error);
      message.error(`❌ 添加用户失败: ${error.response?.data?.detail || error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // 密码强度验证
  const validatePassword = (_: any, value: string) => {
    if (!value) {
      return Promise.reject(new Error('请输入密码'));
    }
    if (value.length < 12) {
      return Promise.reject(new Error('密码长度必须至少12位'));
    }
    if (!/[A-Z]/.test(value)) {
      return Promise.reject(new Error('密码必须包含至少一个大写字母'));
    }
    if (!/[a-z]/.test(value)) {
      return Promise.reject(new Error('密码必须包含至少一个小写字母'));
    }
    if (!/\d/.test(value)) {
      return Promise.reject(new Error('密码必须包含至少一个数字'));
    }
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(value)) {
      return Promise.reject(new Error('密码必须包含至少一个特殊字符'));
    }
    return Promise.resolve();
  };

  // 确认密码验证
  const validateConfirmPassword = (_: any, value: string) => {
    const form = changePasswordForm || addUserForm;
    const passwordField = form === changePasswordForm ? 'new_password' : 'new_user_password';
    const password = form.getFieldValue(passwordField);

    if (!value) {
      return Promise.reject(new Error('请确认密码'));
    }
    if (value !== password) {
      return Promise.reject(new Error('两次输入的密码不一致'));
    }
    return Promise.resolve();
  };

  // 用户列表表格列定义
  const userColumns = [
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
      render: (username: string) => (
        <Space>
          <UserOutlined />
          <Text strong>{username}</Text>
        </Space>
      ),
    },
    {
      title: '角色',
      dataIndex: 'is_admin',
      key: 'is_admin',
      render: (isAdmin: boolean) => (
        <Tag color={isAdmin ? 'red' : 'blue'}>
          {isAdmin ? '管理员' : '普通用户'}
        </Tag>
      ),
    },
  ];

  return (
    <div>
      <Title level={3} style={{ fontSize: '20px', fontWeight: 600, marginBottom: '8px' }}>
        用户管理
      </Title>
      <Text type="secondary">
        修改密码、添加新用户等用户管理功能。
      </Text>

      <Tabs defaultActiveKey="1" style={{ marginTop: 24 }}>
        <TabPane tab={<span><KeyOutlined />密码管理</span>} key="1">
          <Card title="密码管理" size="small">
            <Alert
              message="密码安全提示"
              description="为了账户安全，建议定期修改密码。新密码必须至少12位，包含大写字母、小写字母、数字和特殊字符。"
              type="info"
              showIcon
              style={{ marginBottom: 24 }}
            />

            <Form
              form={changePasswordForm}
              layout="vertical"
              onFinish={handleChangePassword}
              style={{ maxWidth: 600 }}
            >
                <Form.Item
                  label="当前用户"
                  name="current_user"
                  initialValue={currentUser}
                >
                  <Input
                    prefix={<UserOutlined />}
                    disabled
                    value={currentUser}
                  />
                </Form.Item>

                <Form.Item
                  label="原密码"
                  name="old_password"
                  rules={[{ required: true, message: '请输入原密码' }]}
                >
                  <Input.Password
                    prefix={<LockOutlined />}
                    placeholder="请输入原密码"
                    iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
                  />
                </Form.Item>

                <Form.Item
                  label="新密码"
                  name="new_password"
                  rules={[{ validator: validatePassword }]}
                >
                  <Input.Password
                    prefix={<SafetyOutlined />}
                    placeholder="请输入新密码（至少12位，包含大小写字母、数字和特殊字符）"
                    iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
                  />
                </Form.Item>

                <Form.Item
                  label="确认新密码"
                  name="confirm_password"
                  rules={[{ validator: validateConfirmPassword }]}
                >
                  <Input.Password
                    prefix={<SafetyOutlined />}
                    placeholder="请再次输入新密码"
                    iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
                  />
                </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  icon={<LockOutlined />}
                  size="large"
                >
                  修改密码
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </TabPane>

        {/* 用户管理（仅管理员） */}
        {isAdmin && (
          <TabPane tab={<span><UserAddOutlined />添加用户</span>} key="2">
            <Card title="添加新用户" size="small">
              <Alert
                message="管理员功能"
                description="此功能仅限管理员使用，可以添加新用户到系统中。"
                type="info"
                showIcon
                style={{ marginBottom: 24 }}
              />

              <Form
                form={addUserForm}
                layout="vertical"
                onFinish={handleAddUser}
                style={{ maxWidth: 600 }}
              >
                  <Form.Item
                    label="新用户名"
                    name="new_username"
                    rules={[
                      { required: true, message: '请输入用户名' },
                      { min: 3, message: '用户名至少3位' },
                      { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线' }
                    ]}
                  >
                    <Input
                      prefix={<UserOutlined />}
                      placeholder="请输入新用户名"
                    />
                  </Form.Item>

                  <Form.Item
                    label="新用户密码"
                    name="new_user_password"
                    rules={[{ validator: validatePassword }]}
                  >
                    <Input.Password
                      prefix={<LockOutlined />}
                      placeholder="请输入新用户密码（至少12位，包含大小写字母、数字和特殊字符）"
                      iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
                    />
                  </Form.Item>

                  <Form.Item
                    label="确认新用户密码"
                    name="confirm_user_password"
                    rules={[{ validator: validateConfirmPassword }]}
                  >
                    <Input.Password
                      prefix={<SafetyOutlined />}
                      placeholder="请再次输入新用户密码"
                      iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
                    />
                  </Form.Item>

                <Form.Item>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={loading}
                    icon={<UserAddOutlined />}
                    size="large"
                  >
                    添加用户
                  </Button>
                </Form.Item>
              </Form>
            </Card>
          </TabPane>
        )}

        {isAdmin && (
          <TabPane tab={<span><SettingOutlined />用户列表</span>} key="3">
            <Card
              title="用户列表"
              size="small"
              extra={
                <Space>
                  <Button
                    type="primary"
                    icon={<ReloadOutlined />}
                    onClick={fetchUsers}
                    loading={loading}
                  >
                    刷新
                  </Button>
                  <Tag color="blue">{users.length} 个用户</Tag>
                </Space>
              }
            >
              <Table
                columns={userColumns}
                dataSource={users}
                rowKey="username"
                pagination={{
                  pageSize: 10,
                  showSizeChanger: true,
                  showTotal: (total) => `共 ${total} 个用户`,
                }}
                size="small"
              />
            </Card>
          </TabPane>
        )}

        {/* 非管理员提示 */}
        {!isAdmin && (
          <TabPane tab={<span><ExclamationCircleOutlined />权限说明</span>} key="2">
            <Card title="权限说明" size="small">
              <Alert
                message="权限提示"
                description="您当前是普通用户，只能修改自己的密码。如需添加新用户或查看用户列表，请联系管理员。"
                type="warning"
                showIcon
                icon={<ExclamationCircleOutlined />}
              />
            </Card>
          </TabPane>
        )}
      </Tabs>
    </div>
  );
};

export default UserManagementPage;
