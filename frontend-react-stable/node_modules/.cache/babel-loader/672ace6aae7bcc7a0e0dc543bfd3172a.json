{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/frontend-react-stable/src/pages/UserManagementPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { Typography, Card, Alert, Form, Input, Button, message, Space, Table, Tag, Tabs } from 'antd';\nimport { LockOutlined, UserAddOutlined, UserOutlined, EyeInvisibleOutlined, EyeTwoTone, SafetyOutlined, ExclamationCircleOutlined, SettingOutlined, KeyOutlined, ReloadOutlined } from '@ant-design/icons';\nimport { useSelector } from 'react-redux';\nimport { authAPI } from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  TabPane\n} = Tabs;\n// const { confirm } = Modal;\n\nconst UserManagementPage = () => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [users, setUsers] = useState([]);\n  const [changePasswordForm] = Form.useForm();\n  const [addUserForm] = Form.useForm();\n\n  // 从Redux store获取当前用户信息\n  const {\n    user,\n    token\n  } = useSelector(state => state.auth);\n  const currentUser = (user === null || user === void 0 ? void 0 : user.username) || '';\n  const isAdmin = currentUser === 'admin';\n\n  // 获取用户列表（仅管理员）\n  const fetchUsers = useCallback(async () => {\n    if (!token) return;\n    try {\n      const response = await authAPI.getUsers(token);\n      if (response.data) {\n        // 将用户对象转换为数组格式\n        const userList = Object.entries(response.data).map(([username]) => ({\n          username,\n          is_admin: username === 'admin'\n        }));\n        setUsers(userList);\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('获取用户列表失败:', error);\n      message.error(`❌ 获取用户列表失败: ${((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || error.message}`);\n    }\n  }, [token]);\n\n  // 获取当前用户信息\n  useEffect(() => {\n    if (isAdmin && token) {\n      fetchUsers();\n    }\n  }, [isAdmin, token, fetchUsers]);\n\n  // 修改密码\n  const handleChangePassword = async values => {\n    if (!token) return;\n    setLoading(true);\n    try {\n      const response = await authAPI.changePassword({\n        username: currentUser,\n        old_password: values.old_password,\n        new_password: values.new_password,\n        confirm_password: values.confirm_password\n      }, token);\n      if (response.data.message) {\n        message.success('✅ 密码修改成功');\n        changePasswordForm.resetFields();\n      }\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      console.error('修改密码失败:', error);\n      message.error(`❌ 修改密码失败: ${((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail) || error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 添加用户（仅管理员）\n  const handleAddUser = async values => {\n    if (!token) return;\n    setLoading(true);\n    try {\n      const response = await authAPI.addUser({\n        username: currentUser,\n        new_username: values.new_username,\n        new_user_password: values.new_user_password,\n        confirm_user_password: values.confirm_user_password\n      }, token);\n      if (response.data.message) {\n        message.success('✅ 用户添加成功');\n        addUserForm.resetFields();\n        fetchUsers(); // 刷新用户列表\n      }\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      console.error('添加用户失败:', error);\n      message.error(`❌ 添加用户失败: ${((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.detail) || error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 密码强度验证\n  const validatePassword = (_, value) => {\n    if (!value) {\n      return Promise.reject(new Error('请输入密码'));\n    }\n    if (value.length < 12) {\n      return Promise.reject(new Error('密码长度必须至少12位'));\n    }\n    if (!/[A-Z]/.test(value)) {\n      return Promise.reject(new Error('密码必须包含至少一个大写字母'));\n    }\n    if (!/[a-z]/.test(value)) {\n      return Promise.reject(new Error('密码必须包含至少一个小写字母'));\n    }\n    if (!/\\d/.test(value)) {\n      return Promise.reject(new Error('密码必须包含至少一个数字'));\n    }\n    if (!/[!@#$%^&*(),.?\":{}|<>]/.test(value)) {\n      return Promise.reject(new Error('密码必须包含至少一个特殊字符'));\n    }\n    return Promise.resolve();\n  };\n\n  // 确认密码验证\n  const validateConfirmPassword = (_, value) => {\n    const form = changePasswordForm || addUserForm;\n    const passwordField = form === changePasswordForm ? 'new_password' : 'new_user_password';\n    const password = form.getFieldValue(passwordField);\n    if (!value) {\n      return Promise.reject(new Error('请确认密码'));\n    }\n    if (value !== password) {\n      return Promise.reject(new Error('两次输入的密码不一致'));\n    }\n    return Promise.resolve();\n  };\n\n  // 用户列表表格列定义\n  const userColumns = [{\n    title: '用户名',\n    dataIndex: 'username',\n    key: 'username',\n    render: username => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        strong: true,\n        children: username\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 174,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '角色',\n    dataIndex: 'is_admin',\n    key: 'is_admin',\n    render: isAdmin => /*#__PURE__*/_jsxDEV(Tag, {\n      color: isAdmin ? 'red' : 'blue',\n      children: isAdmin ? '管理员' : '普通用户'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 185,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 3,\n      style: {\n        fontSize: '20px',\n        fontWeight: 600,\n        marginBottom: '8px'\n      },\n      children: \"\\u7528\\u6237\\u7BA1\\u7406\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 194,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Text, {\n      type: \"secondary\",\n      children: \"\\u4FEE\\u6539\\u5BC6\\u7801\\u3001\\u6DFB\\u52A0\\u65B0\\u7528\\u6237\\u7B49\\u7528\\u6237\\u7BA1\\u7406\\u529F\\u80FD\\u3002\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 197,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Tabs, {\n      defaultActiveKey: \"1\",\n      style: {\n        marginTop: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(TabPane, {\n        tab: /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [/*#__PURE__*/_jsxDEV(KeyOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 29\n          }, this), \"\\u5BC6\\u7801\\u7BA1\\u7406\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 23\n        }, this),\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u5BC6\\u7801\\u7BA1\\u7406\",\n          size: \"small\",\n          children: [/*#__PURE__*/_jsxDEV(Alert, {\n            message: \"\\u5BC6\\u7801\\u5B89\\u5168\\u63D0\\u793A\",\n            description: \"\\u4E3A\\u4E86\\u8D26\\u6237\\u5B89\\u5168\\uFF0C\\u5EFA\\u8BAE\\u5B9A\\u671F\\u4FEE\\u6539\\u5BC6\\u7801\\u3002\\u65B0\\u5BC6\\u7801\\u5FC5\\u987B\\u81F3\\u5C1112\\u4F4D\\uFF0C\\u5305\\u542B\\u5927\\u5199\\u5B57\\u6BCD\\u3001\\u5C0F\\u5199\\u5B57\\u6BCD\\u3001\\u6570\\u5B57\\u548C\\u7279\\u6B8A\\u5B57\\u7B26\\u3002\",\n            type: \"info\",\n            showIcon: true,\n            style: {\n              marginBottom: 24\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form, {\n            form: changePasswordForm,\n            layout: \"vertical\",\n            onFinish: handleChangePassword,\n            style: {\n              maxWidth: 600\n            },\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u5F53\\u524D\\u7528\\u6237\",\n              name: \"current_user\",\n              initialValue: currentUser,\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                prefix: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 29\n                }, this),\n                disabled: true,\n                value: currentUser\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u539F\\u5BC6\\u7801\",\n              name: \"old_password\",\n              rules: [{\n                required: true,\n                message: '请输入原密码'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input.Password, {\n                prefix: /*#__PURE__*/_jsxDEV(LockOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 29\n                }, this),\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u539F\\u5BC6\\u7801\",\n                iconRender: visible => visible ? /*#__PURE__*/_jsxDEV(EyeTwoTone, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 238,\n                  columnNumber: 57\n                }, this) : /*#__PURE__*/_jsxDEV(EyeInvisibleOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 238,\n                  columnNumber: 74\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u65B0\\u5BC6\\u7801\",\n              name: \"new_password\",\n              rules: [{\n                validator: validatePassword\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input.Password, {\n                prefix: /*#__PURE__*/_jsxDEV(SafetyOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 29\n                }, this),\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u65B0\\u5BC6\\u7801\\uFF08\\u81F3\\u5C1112\\u4F4D\\uFF0C\\u5305\\u542B\\u5927\\u5C0F\\u5199\\u5B57\\u6BCD\\u3001\\u6570\\u5B57\\u548C\\u7279\\u6B8A\\u5B57\\u7B26\\uFF09\",\n                iconRender: visible => visible ? /*#__PURE__*/_jsxDEV(EyeTwoTone, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 250,\n                  columnNumber: 57\n                }, this) : /*#__PURE__*/_jsxDEV(EyeInvisibleOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 250,\n                  columnNumber: 74\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u786E\\u8BA4\\u65B0\\u5BC6\\u7801\",\n              name: \"confirm_password\",\n              rules: [{\n                validator: validateConfirmPassword\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input.Password, {\n                prefix: /*#__PURE__*/_jsxDEV(SafetyOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 260,\n                  columnNumber: 29\n                }, this),\n                placeholder: \"\\u8BF7\\u518D\\u6B21\\u8F93\\u5165\\u65B0\\u5BC6\\u7801\",\n                iconRender: visible => visible ? /*#__PURE__*/_jsxDEV(EyeTwoTone, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 262,\n                  columnNumber: 57\n                }, this) : /*#__PURE__*/_jsxDEV(EyeInvisibleOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 262,\n                  columnNumber: 74\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                type: \"primary\",\n                htmlType: \"submit\",\n                loading: loading,\n                icon: /*#__PURE__*/_jsxDEV(LockOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 271,\n                  columnNumber: 25\n                }, this),\n                size: \"large\",\n                children: \"\\u4FEE\\u6539\\u5BC6\\u7801\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this)\n      }, \"1\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 9\n      }, this), isAdmin && /*#__PURE__*/_jsxDEV(TabPane, {\n        tab: /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [/*#__PURE__*/_jsxDEV(UserAddOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 31\n          }, this), \"\\u6DFB\\u52A0\\u7528\\u6237\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 25\n        }, this),\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u6DFB\\u52A0\\u65B0\\u7528\\u6237\",\n          size: \"small\",\n          children: [/*#__PURE__*/_jsxDEV(Alert, {\n            message: \"\\u7BA1\\u7406\\u5458\\u529F\\u80FD\",\n            description: \"\\u6B64\\u529F\\u80FD\\u4EC5\\u9650\\u7BA1\\u7406\\u5458\\u4F7F\\u7528\\uFF0C\\u53EF\\u4EE5\\u6DFB\\u52A0\\u65B0\\u7528\\u6237\\u5230\\u7CFB\\u7EDF\\u4E2D\\u3002\",\n            type: \"info\",\n            showIcon: true,\n            style: {\n              marginBottom: 24\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form, {\n            form: addUserForm,\n            layout: \"vertical\",\n            onFinish: handleAddUser,\n            style: {\n              maxWidth: 600\n            },\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u65B0\\u7528\\u6237\\u540D\",\n              name: \"new_username\",\n              rules: [{\n                required: true,\n                message: '请输入用户名'\n              }, {\n                min: 3,\n                message: '用户名至少3位'\n              }, {\n                pattern: /^[a-zA-Z0-9_]+$/,\n                message: '用户名只能包含字母、数字和下划线'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                prefix: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 309,\n                  columnNumber: 31\n                }, this),\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u65B0\\u7528\\u6237\\u540D\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u65B0\\u7528\\u6237\\u5BC6\\u7801\",\n              name: \"new_user_password\",\n              rules: [{\n                validator: validatePassword\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input.Password, {\n                prefix: /*#__PURE__*/_jsxDEV(LockOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 320,\n                  columnNumber: 31\n                }, this),\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u65B0\\u7528\\u6237\\u5BC6\\u7801\\uFF08\\u81F3\\u5C1112\\u4F4D\\uFF0C\\u5305\\u542B\\u5927\\u5C0F\\u5199\\u5B57\\u6BCD\\u3001\\u6570\\u5B57\\u548C\\u7279\\u6B8A\\u5B57\\u7B26\\uFF09\",\n                iconRender: visible => visible ? /*#__PURE__*/_jsxDEV(EyeTwoTone, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 322,\n                  columnNumber: 59\n                }, this) : /*#__PURE__*/_jsxDEV(EyeInvisibleOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 322,\n                  columnNumber: 76\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u786E\\u8BA4\\u65B0\\u7528\\u6237\\u5BC6\\u7801\",\n              name: \"confirm_user_password\",\n              rules: [{\n                validator: validateConfirmPassword\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input.Password, {\n                prefix: /*#__PURE__*/_jsxDEV(SafetyOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 332,\n                  columnNumber: 31\n                }, this),\n                placeholder: \"\\u8BF7\\u518D\\u6B21\\u8F93\\u5165\\u65B0\\u7528\\u6237\\u5BC6\\u7801\",\n                iconRender: visible => visible ? /*#__PURE__*/_jsxDEV(EyeTwoTone, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 334,\n                  columnNumber: 59\n                }, this) : /*#__PURE__*/_jsxDEV(EyeInvisibleOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 334,\n                  columnNumber: 76\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                type: \"primary\",\n                htmlType: \"submit\",\n                loading: loading,\n                icon: /*#__PURE__*/_jsxDEV(UserAddOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 343,\n                  columnNumber: 27\n                }, this),\n                size: \"large\",\n                children: \"\\u6DFB\\u52A0\\u7528\\u6237\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 13\n        }, this)\n      }, \"2\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 11\n      }, this), isAdmin && /*#__PURE__*/_jsxDEV(TabPane, {\n        tab: /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [/*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 355,\n            columnNumber: 31\n          }, this), \"\\u7528\\u6237\\u5217\\u8868\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 355,\n          columnNumber: 25\n        }, this),\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u7528\\u6237\\u5217\\u8868\",\n          size: \"small\",\n          extra: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 363,\n                columnNumber: 27\n              }, this),\n              onClick: fetchUsers,\n              loading: loading,\n              children: \"\\u5237\\u65B0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 361,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Tag, {\n              color: \"blue\",\n              children: [users.length, \" \\u4E2A\\u7528\\u6237\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 369,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 17\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            columns: userColumns,\n            dataSource: users,\n            rowKey: \"username\",\n            pagination: {\n              pageSize: 10,\n              showSizeChanger: true,\n              showTotal: total => `共 ${total} 个用户`\n            },\n            size: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 373,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 356,\n          columnNumber: 13\n        }, this)\n      }, \"3\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 355,\n        columnNumber: 11\n      }, this), !isAdmin && /*#__PURE__*/_jsxDEV(TabPane, {\n        tab: /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [/*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 31\n          }, this), \"\\u6743\\u9650\\u8BF4\\u660E\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 390,\n          columnNumber: 25\n        }, this),\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u6743\\u9650\\u8BF4\\u660E\",\n          size: \"small\",\n          children: /*#__PURE__*/_jsxDEV(Alert, {\n            message: \"\\u6743\\u9650\\u63D0\\u793A\",\n            description: \"\\u60A8\\u5F53\\u524D\\u662F\\u666E\\u901A\\u7528\\u6237\\uFF0C\\u53EA\\u80FD\\u4FEE\\u6539\\u81EA\\u5DF1\\u7684\\u5BC6\\u7801\\u3002\\u5982\\u9700\\u6DFB\\u52A0\\u65B0\\u7528\\u6237\\u6216\\u67E5\\u770B\\u7528\\u6237\\u5217\\u8868\\uFF0C\\u8BF7\\u8054\\u7CFB\\u7BA1\\u7406\\u5458\\u3002\",\n            type: \"warning\",\n            showIcon: true,\n            icon: /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 397,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 392,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 391,\n          columnNumber: 13\n        }, this)\n      }, \"2\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 390,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 201,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 193,\n    columnNumber: 5\n  }, this);\n};\n_s(UserManagementPage, \"kvxNulDRolVLK02a1i70i9o5XOY=\", false, function () {\n  return [Form.useForm, Form.useForm, useSelector];\n});\n_c = UserManagementPage;\nexport default UserManagementPage;\nvar _c;\n$RefreshReg$(_c, \"UserManagementPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "Typography", "Card", "<PERSON><PERSON>", "Form", "Input", "<PERSON><PERSON>", "message", "Space", "Table", "Tag", "Tabs", "LockOutlined", "UserAddOutlined", "UserOutlined", "EyeInvisibleOutlined", "EyeTwoTone", "SafetyOutlined", "ExclamationCircleOutlined", "SettingOutlined", "KeyOutlined", "ReloadOutlined", "useSelector", "authAPI", "jsxDEV", "_jsxDEV", "Title", "Text", "TabPane", "UserManagementPage", "_s", "loading", "setLoading", "users", "setUsers", "changePasswordForm", "useForm", "addUserForm", "user", "token", "state", "auth", "currentUser", "username", "isAdmin", "fetchUsers", "response", "getUsers", "data", "userList", "Object", "entries", "map", "is_admin", "error", "_error$response", "_error$response$data", "console", "detail", "handleChangePassword", "values", "changePassword", "old_password", "new_password", "confirm_password", "success", "resetFields", "_error$response2", "_error$response2$data", "handleAddUser", "addUser", "new_username", "new_user_password", "confirm_user_password", "_error$response3", "_error$response3$data", "validatePassword", "_", "value", "Promise", "reject", "Error", "length", "test", "resolve", "validateConfirmPassword", "form", "passwordField", "password", "getFieldValue", "userColumns", "title", "dataIndex", "key", "render", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "strong", "color", "level", "style", "fontSize", "fontWeight", "marginBottom", "type", "defaultActiveKey", "marginTop", "tab", "size", "description", "showIcon", "layout", "onFinish", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "label", "name", "initialValue", "prefix", "disabled", "rules", "required", "Password", "placeholder", "iconRender", "visible", "validator", "htmlType", "icon", "min", "pattern", "extra", "onClick", "columns", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "pageSize", "showSizeChanger", "showTotal", "total", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/frontend-react-stable/src/pages/UserManagementPage.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport {\n  Typography,\n  Card,\n  Alert,\n  Form,\n  Input,\n  Button,\n  message,\n  Space,\n  Table,\n  Tag,\n  Tabs,\n} from 'antd';\nimport {\n  LockOutlined,\n  UserAddOutlined,\n  UserOutlined,\n  EyeInvisibleOutlined,\n  EyeTwoTone,\n  SafetyOutlined,\n  ExclamationCircleOutlined,\n  SettingOutlined,\n  KeyOutlined,\n  ReloadOutlined,\n} from '@ant-design/icons';\nimport { useSelector } from 'react-redux';\nimport { RootState } from '../store/store';\nimport { authAPI } from '../services/api';\n\nconst { Title, Text } = Typography;\nconst { TabPane } = Tabs;\n// const { confirm } = Modal;\n\ninterface User {\n  username: string;\n  is_admin: boolean;\n}\n\nconst UserManagementPage: React.FC = () => {\n  const [loading, setLoading] = useState(false);\n  const [users, setUsers] = useState<User[]>([]);\n  const [changePasswordForm] = Form.useForm();\n  const [addUserForm] = Form.useForm();\n\n  // 从Redux store获取当前用户信息\n  const { user, token } = useSelector((state: RootState) => state.auth);\n  const currentUser = user?.username || '';\n  const isAdmin = currentUser === 'admin';\n\n  // 获取用户列表（仅管理员）\n  const fetchUsers = useCallback(async () => {\n    if (!token) return;\n\n    try {\n      const response = await authAPI.getUsers(token);\n      if (response.data) {\n        // 将用户对象转换为数组格式\n        const userList = Object.entries(response.data).map(([username]: [string, any]) => ({\n          username,\n          is_admin: username === 'admin',\n        }));\n        setUsers(userList);\n      }\n    } catch (error: any) {\n      console.error('获取用户列表失败:', error);\n      message.error(`❌ 获取用户列表失败: ${error.response?.data?.detail || error.message}`);\n    }\n  }, [token]);\n\n  // 获取当前用户信息\n  useEffect(() => {\n    if (isAdmin && token) {\n      fetchUsers();\n    }\n  }, [isAdmin, token, fetchUsers]);\n\n  // 修改密码\n  const handleChangePassword = async (values: any) => {\n    if (!token) return;\n\n    setLoading(true);\n    try {\n      const response = await authAPI.changePassword({\n        username: currentUser,\n        old_password: values.old_password,\n        new_password: values.new_password,\n        confirm_password: values.confirm_password\n      }, token);\n\n      if (response.data.message) {\n        message.success('✅ 密码修改成功');\n        changePasswordForm.resetFields();\n      }\n    } catch (error: any) {\n      console.error('修改密码失败:', error);\n      message.error(`❌ 修改密码失败: ${error.response?.data?.detail || error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 添加用户（仅管理员）\n  const handleAddUser = async (values: any) => {\n    if (!token) return;\n\n    setLoading(true);\n    try {\n      const response = await authAPI.addUser({\n        username: currentUser,\n        new_username: values.new_username,\n        new_user_password: values.new_user_password,\n        confirm_user_password: values.confirm_user_password\n      }, token);\n\n      if (response.data.message) {\n        message.success('✅ 用户添加成功');\n        addUserForm.resetFields();\n        fetchUsers(); // 刷新用户列表\n      }\n    } catch (error: any) {\n      console.error('添加用户失败:', error);\n      message.error(`❌ 添加用户失败: ${error.response?.data?.detail || error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 密码强度验证\n  const validatePassword = (_: any, value: string) => {\n    if (!value) {\n      return Promise.reject(new Error('请输入密码'));\n    }\n    if (value.length < 12) {\n      return Promise.reject(new Error('密码长度必须至少12位'));\n    }\n    if (!/[A-Z]/.test(value)) {\n      return Promise.reject(new Error('密码必须包含至少一个大写字母'));\n    }\n    if (!/[a-z]/.test(value)) {\n      return Promise.reject(new Error('密码必须包含至少一个小写字母'));\n    }\n    if (!/\\d/.test(value)) {\n      return Promise.reject(new Error('密码必须包含至少一个数字'));\n    }\n    if (!/[!@#$%^&*(),.?\":{}|<>]/.test(value)) {\n      return Promise.reject(new Error('密码必须包含至少一个特殊字符'));\n    }\n    return Promise.resolve();\n  };\n\n  // 确认密码验证\n  const validateConfirmPassword = (_: any, value: string) => {\n    const form = changePasswordForm || addUserForm;\n    const passwordField = form === changePasswordForm ? 'new_password' : 'new_user_password';\n    const password = form.getFieldValue(passwordField);\n\n    if (!value) {\n      return Promise.reject(new Error('请确认密码'));\n    }\n    if (value !== password) {\n      return Promise.reject(new Error('两次输入的密码不一致'));\n    }\n    return Promise.resolve();\n  };\n\n  // 用户列表表格列定义\n  const userColumns = [\n    {\n      title: '用户名',\n      dataIndex: 'username',\n      key: 'username',\n      render: (username: string) => (\n        <Space>\n          <UserOutlined />\n          <Text strong>{username}</Text>\n        </Space>\n      ),\n    },\n    {\n      title: '角色',\n      dataIndex: 'is_admin',\n      key: 'is_admin',\n      render: (isAdmin: boolean) => (\n        <Tag color={isAdmin ? 'red' : 'blue'}>\n          {isAdmin ? '管理员' : '普通用户'}\n        </Tag>\n      ),\n    },\n  ];\n\n  return (\n    <div>\n      <Title level={3} style={{ fontSize: '20px', fontWeight: 600, marginBottom: '8px' }}>\n        用户管理\n      </Title>\n      <Text type=\"secondary\">\n        修改密码、添加新用户等用户管理功能。\n      </Text>\n\n      <Tabs defaultActiveKey=\"1\" style={{ marginTop: 24 }}>\n        <TabPane tab={<span><KeyOutlined />密码管理</span>} key=\"1\">\n          <Card title=\"密码管理\" size=\"small\">\n            <Alert\n              message=\"密码安全提示\"\n              description=\"为了账户安全，建议定期修改密码。新密码必须至少12位，包含大写字母、小写字母、数字和特殊字符。\"\n              type=\"info\"\n              showIcon\n              style={{ marginBottom: 24 }}\n            />\n\n            <Form\n              form={changePasswordForm}\n              layout=\"vertical\"\n              onFinish={handleChangePassword}\n              style={{ maxWidth: 600 }}\n            >\n                <Form.Item\n                  label=\"当前用户\"\n                  name=\"current_user\"\n                  initialValue={currentUser}\n                >\n                  <Input\n                    prefix={<UserOutlined />}\n                    disabled\n                    value={currentUser}\n                  />\n                </Form.Item>\n\n                <Form.Item\n                  label=\"原密码\"\n                  name=\"old_password\"\n                  rules={[{ required: true, message: '请输入原密码' }]}\n                >\n                  <Input.Password\n                    prefix={<LockOutlined />}\n                    placeholder=\"请输入原密码\"\n                    iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}\n                  />\n                </Form.Item>\n\n                <Form.Item\n                  label=\"新密码\"\n                  name=\"new_password\"\n                  rules={[{ validator: validatePassword }]}\n                >\n                  <Input.Password\n                    prefix={<SafetyOutlined />}\n                    placeholder=\"请输入新密码（至少12位，包含大小写字母、数字和特殊字符）\"\n                    iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}\n                  />\n                </Form.Item>\n\n                <Form.Item\n                  label=\"确认新密码\"\n                  name=\"confirm_password\"\n                  rules={[{ validator: validateConfirmPassword }]}\n                >\n                  <Input.Password\n                    prefix={<SafetyOutlined />}\n                    placeholder=\"请再次输入新密码\"\n                    iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}\n                  />\n                </Form.Item>\n\n              <Form.Item>\n                <Button\n                  type=\"primary\"\n                  htmlType=\"submit\"\n                  loading={loading}\n                  icon={<LockOutlined />}\n                  size=\"large\"\n                >\n                  修改密码\n                </Button>\n              </Form.Item>\n            </Form>\n          </Card>\n        </TabPane>\n\n        {/* 用户管理（仅管理员） */}\n        {isAdmin && (\n          <TabPane tab={<span><UserAddOutlined />添加用户</span>} key=\"2\">\n            <Card title=\"添加新用户\" size=\"small\">\n              <Alert\n                message=\"管理员功能\"\n                description=\"此功能仅限管理员使用，可以添加新用户到系统中。\"\n                type=\"info\"\n                showIcon\n                style={{ marginBottom: 24 }}\n              />\n\n              <Form\n                form={addUserForm}\n                layout=\"vertical\"\n                onFinish={handleAddUser}\n                style={{ maxWidth: 600 }}\n              >\n                  <Form.Item\n                    label=\"新用户名\"\n                    name=\"new_username\"\n                    rules={[\n                      { required: true, message: '请输入用户名' },\n                      { min: 3, message: '用户名至少3位' },\n                      { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线' }\n                    ]}\n                  >\n                    <Input\n                      prefix={<UserOutlined />}\n                      placeholder=\"请输入新用户名\"\n                    />\n                  </Form.Item>\n\n                  <Form.Item\n                    label=\"新用户密码\"\n                    name=\"new_user_password\"\n                    rules={[{ validator: validatePassword }]}\n                  >\n                    <Input.Password\n                      prefix={<LockOutlined />}\n                      placeholder=\"请输入新用户密码（至少12位，包含大小写字母、数字和特殊字符）\"\n                      iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}\n                    />\n                  </Form.Item>\n\n                  <Form.Item\n                    label=\"确认新用户密码\"\n                    name=\"confirm_user_password\"\n                    rules={[{ validator: validateConfirmPassword }]}\n                  >\n                    <Input.Password\n                      prefix={<SafetyOutlined />}\n                      placeholder=\"请再次输入新用户密码\"\n                      iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}\n                    />\n                  </Form.Item>\n\n                <Form.Item>\n                  <Button\n                    type=\"primary\"\n                    htmlType=\"submit\"\n                    loading={loading}\n                    icon={<UserAddOutlined />}\n                    size=\"large\"\n                  >\n                    添加用户\n                  </Button>\n                </Form.Item>\n              </Form>\n            </Card>\n          </TabPane>\n        )}\n\n        {isAdmin && (\n          <TabPane tab={<span><SettingOutlined />用户列表</span>} key=\"3\">\n            <Card\n              title=\"用户列表\"\n              size=\"small\"\n              extra={\n                <Space>\n                  <Button\n                    type=\"primary\"\n                    icon={<ReloadOutlined />}\n                    onClick={fetchUsers}\n                    loading={loading}\n                  >\n                    刷新\n                  </Button>\n                  <Tag color=\"blue\">{users.length} 个用户</Tag>\n                </Space>\n              }\n            >\n              <Table\n                columns={userColumns}\n                dataSource={users}\n                rowKey=\"username\"\n                pagination={{\n                  pageSize: 10,\n                  showSizeChanger: true,\n                  showTotal: (total) => `共 ${total} 个用户`,\n                }}\n                size=\"small\"\n              />\n            </Card>\n          </TabPane>\n        )}\n\n        {/* 非管理员提示 */}\n        {!isAdmin && (\n          <TabPane tab={<span><ExclamationCircleOutlined />权限说明</span>} key=\"2\">\n            <Card title=\"权限说明\" size=\"small\">\n              <Alert\n                message=\"权限提示\"\n                description=\"您当前是普通用户，只能修改自己的密码。如需添加新用户或查看用户列表，请联系管理员。\"\n                type=\"warning\"\n                showIcon\n                icon={<ExclamationCircleOutlined />}\n              />\n            </Card>\n          </TabPane>\n        )}\n      </Tabs>\n    </div>\n  );\n};\n\nexport default UserManagementPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SACEC,UAAU,EACVC,IAAI,EACJC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,OAAO,EACPC,KAAK,EACLC,KAAK,EACLC,GAAG,EACHC,IAAI,QACC,MAAM;AACb,SACEC,YAAY,EACZC,eAAe,EACfC,YAAY,EACZC,oBAAoB,EACpBC,UAAU,EACVC,cAAc,EACdC,yBAAyB,EACzBC,eAAe,EACfC,WAAW,EACXC,cAAc,QACT,mBAAmB;AAC1B,SAASC,WAAW,QAAQ,aAAa;AAEzC,SAASC,OAAO,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAG1B,UAAU;AAClC,MAAM;EAAE2B;AAAQ,CAAC,GAAGjB,IAAI;AACxB;;AAOA,MAAMkB,kBAA4B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACmC,KAAK,EAAEC,QAAQ,CAAC,GAAGpC,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAACqC,kBAAkB,CAAC,GAAG/B,IAAI,CAACgC,OAAO,CAAC,CAAC;EAC3C,MAAM,CAACC,WAAW,CAAC,GAAGjC,IAAI,CAACgC,OAAO,CAAC,CAAC;;EAEpC;EACA,MAAM;IAAEE,IAAI;IAAEC;EAAM,CAAC,GAAGjB,WAAW,CAAEkB,KAAgB,IAAKA,KAAK,CAACC,IAAI,CAAC;EACrE,MAAMC,WAAW,GAAG,CAAAJ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEK,QAAQ,KAAI,EAAE;EACxC,MAAMC,OAAO,GAAGF,WAAW,KAAK,OAAO;;EAEvC;EACA,MAAMG,UAAU,GAAG7C,WAAW,CAAC,YAAY;IACzC,IAAI,CAACuC,KAAK,EAAE;IAEZ,IAAI;MACF,MAAMO,QAAQ,GAAG,MAAMvB,OAAO,CAACwB,QAAQ,CAACR,KAAK,CAAC;MAC9C,IAAIO,QAAQ,CAACE,IAAI,EAAE;QACjB;QACA,MAAMC,QAAQ,GAAGC,MAAM,CAACC,OAAO,CAACL,QAAQ,CAACE,IAAI,CAAC,CAACI,GAAG,CAAC,CAAC,CAACT,QAAQ,CAAgB,MAAM;UACjFA,QAAQ;UACRU,QAAQ,EAAEV,QAAQ,KAAK;QACzB,CAAC,CAAC,CAAC;QACHT,QAAQ,CAACe,QAAQ,CAAC;MACpB;IACF,CAAC,CAAC,OAAOK,KAAU,EAAE;MAAA,IAAAC,eAAA,EAAAC,oBAAA;MACnBC,OAAO,CAACH,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC/C,OAAO,CAAC+C,KAAK,CAAC,eAAe,EAAAC,eAAA,GAAAD,KAAK,CAACR,QAAQ,cAAAS,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBP,IAAI,cAAAQ,oBAAA,uBAApBA,oBAAA,CAAsBE,MAAM,KAAIJ,KAAK,CAAC/C,OAAO,EAAE,CAAC;IAC/E;EACF,CAAC,EAAE,CAACgC,KAAK,CAAC,CAAC;;EAEX;EACAxC,SAAS,CAAC,MAAM;IACd,IAAI6C,OAAO,IAAIL,KAAK,EAAE;MACpBM,UAAU,CAAC,CAAC;IACd;EACF,CAAC,EAAE,CAACD,OAAO,EAAEL,KAAK,EAAEM,UAAU,CAAC,CAAC;;EAEhC;EACA,MAAMc,oBAAoB,GAAG,MAAOC,MAAW,IAAK;IAClD,IAAI,CAACrB,KAAK,EAAE;IAEZP,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMc,QAAQ,GAAG,MAAMvB,OAAO,CAACsC,cAAc,CAAC;QAC5ClB,QAAQ,EAAED,WAAW;QACrBoB,YAAY,EAAEF,MAAM,CAACE,YAAY;QACjCC,YAAY,EAAEH,MAAM,CAACG,YAAY;QACjCC,gBAAgB,EAAEJ,MAAM,CAACI;MAC3B,CAAC,EAAEzB,KAAK,CAAC;MAET,IAAIO,QAAQ,CAACE,IAAI,CAACzC,OAAO,EAAE;QACzBA,OAAO,CAAC0D,OAAO,CAAC,UAAU,CAAC;QAC3B9B,kBAAkB,CAAC+B,WAAW,CAAC,CAAC;MAClC;IACF,CAAC,CAAC,OAAOZ,KAAU,EAAE;MAAA,IAAAa,gBAAA,EAAAC,qBAAA;MACnBX,OAAO,CAACH,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B/C,OAAO,CAAC+C,KAAK,CAAC,aAAa,EAAAa,gBAAA,GAAAb,KAAK,CAACR,QAAQ,cAAAqB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBnB,IAAI,cAAAoB,qBAAA,uBAApBA,qBAAA,CAAsBV,MAAM,KAAIJ,KAAK,CAAC/C,OAAO,EAAE,CAAC;IAC7E,CAAC,SAAS;MACRyB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMqC,aAAa,GAAG,MAAOT,MAAW,IAAK;IAC3C,IAAI,CAACrB,KAAK,EAAE;IAEZP,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMc,QAAQ,GAAG,MAAMvB,OAAO,CAAC+C,OAAO,CAAC;QACrC3B,QAAQ,EAAED,WAAW;QACrB6B,YAAY,EAAEX,MAAM,CAACW,YAAY;QACjCC,iBAAiB,EAAEZ,MAAM,CAACY,iBAAiB;QAC3CC,qBAAqB,EAAEb,MAAM,CAACa;MAChC,CAAC,EAAElC,KAAK,CAAC;MAET,IAAIO,QAAQ,CAACE,IAAI,CAACzC,OAAO,EAAE;QACzBA,OAAO,CAAC0D,OAAO,CAAC,UAAU,CAAC;QAC3B5B,WAAW,CAAC6B,WAAW,CAAC,CAAC;QACzBrB,UAAU,CAAC,CAAC,CAAC,CAAC;MAChB;IACF,CAAC,CAAC,OAAOS,KAAU,EAAE;MAAA,IAAAoB,gBAAA,EAAAC,qBAAA;MACnBlB,OAAO,CAACH,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B/C,OAAO,CAAC+C,KAAK,CAAC,aAAa,EAAAoB,gBAAA,GAAApB,KAAK,CAACR,QAAQ,cAAA4B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB1B,IAAI,cAAA2B,qBAAA,uBAApBA,qBAAA,CAAsBjB,MAAM,KAAIJ,KAAK,CAAC/C,OAAO,EAAE,CAAC;IAC7E,CAAC,SAAS;MACRyB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM4C,gBAAgB,GAAGA,CAACC,CAAM,EAAEC,KAAa,KAAK;IAClD,IAAI,CAACA,KAAK,EAAE;MACV,OAAOC,OAAO,CAACC,MAAM,CAAC,IAAIC,KAAK,CAAC,OAAO,CAAC,CAAC;IAC3C;IACA,IAAIH,KAAK,CAACI,MAAM,GAAG,EAAE,EAAE;MACrB,OAAOH,OAAO,CAACC,MAAM,CAAC,IAAIC,KAAK,CAAC,aAAa,CAAC,CAAC;IACjD;IACA,IAAI,CAAC,OAAO,CAACE,IAAI,CAACL,KAAK,CAAC,EAAE;MACxB,OAAOC,OAAO,CAACC,MAAM,CAAC,IAAIC,KAAK,CAAC,gBAAgB,CAAC,CAAC;IACpD;IACA,IAAI,CAAC,OAAO,CAACE,IAAI,CAACL,KAAK,CAAC,EAAE;MACxB,OAAOC,OAAO,CAACC,MAAM,CAAC,IAAIC,KAAK,CAAC,gBAAgB,CAAC,CAAC;IACpD;IACA,IAAI,CAAC,IAAI,CAACE,IAAI,CAACL,KAAK,CAAC,EAAE;MACrB,OAAOC,OAAO,CAACC,MAAM,CAAC,IAAIC,KAAK,CAAC,cAAc,CAAC,CAAC;IAClD;IACA,IAAI,CAAC,wBAAwB,CAACE,IAAI,CAACL,KAAK,CAAC,EAAE;MACzC,OAAOC,OAAO,CAACC,MAAM,CAAC,IAAIC,KAAK,CAAC,gBAAgB,CAAC,CAAC;IACpD;IACA,OAAOF,OAAO,CAACK,OAAO,CAAC,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMC,uBAAuB,GAAGA,CAACR,CAAM,EAAEC,KAAa,KAAK;IACzD,MAAMQ,IAAI,GAAGnD,kBAAkB,IAAIE,WAAW;IAC9C,MAAMkD,aAAa,GAAGD,IAAI,KAAKnD,kBAAkB,GAAG,cAAc,GAAG,mBAAmB;IACxF,MAAMqD,QAAQ,GAAGF,IAAI,CAACG,aAAa,CAACF,aAAa,CAAC;IAElD,IAAI,CAACT,KAAK,EAAE;MACV,OAAOC,OAAO,CAACC,MAAM,CAAC,IAAIC,KAAK,CAAC,OAAO,CAAC,CAAC;IAC3C;IACA,IAAIH,KAAK,KAAKU,QAAQ,EAAE;MACtB,OAAOT,OAAO,CAACC,MAAM,CAAC,IAAIC,KAAK,CAAC,YAAY,CAAC,CAAC;IAChD;IACA,OAAOF,OAAO,CAACK,OAAO,CAAC,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMM,WAAW,GAAG,CAClB;IACEC,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,MAAM,EAAGnD,QAAgB,iBACvBlB,OAAA,CAACjB,KAAK;MAAAuF,QAAA,gBACJtE,OAAA,CAACX,YAAY;QAAAkF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAChB1E,OAAA,CAACE,IAAI;QAACyE,MAAM;QAAAL,QAAA,EAAEpD;MAAQ;QAAAqD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB;EAEX,CAAC,EACD;IACER,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,MAAM,EAAGlD,OAAgB,iBACvBnB,OAAA,CAACf,GAAG;MAAC2F,KAAK,EAAEzD,OAAO,GAAG,KAAK,GAAG,MAAO;MAAAmD,QAAA,EAClCnD,OAAO,GAAG,KAAK,GAAG;IAAM;MAAAoD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB;EAET,CAAC,CACF;EAED,oBACE1E,OAAA;IAAAsE,QAAA,gBACEtE,OAAA,CAACC,KAAK;MAAC4E,KAAK,EAAE,CAAE;MAACC,KAAK,EAAE;QAAEC,QAAQ,EAAE,MAAM;QAAEC,UAAU,EAAE,GAAG;QAAEC,YAAY,EAAE;MAAM,CAAE;MAAAX,QAAA,EAAC;IAEpF;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eACR1E,OAAA,CAACE,IAAI;MAACgF,IAAI,EAAC,WAAW;MAAAZ,QAAA,EAAC;IAEvB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAEP1E,OAAA,CAACd,IAAI;MAACiG,gBAAgB,EAAC,GAAG;MAACL,KAAK,EAAE;QAAEM,SAAS,EAAE;MAAG,CAAE;MAAAd,QAAA,gBAClDtE,OAAA,CAACG,OAAO;QAACkF,GAAG,eAAErF,OAAA;UAAAsE,QAAA,gBAAMtE,OAAA,CAACL,WAAW;YAAA4E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,4BAAI;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAE;QAAAJ,QAAA,eAC7CtE,OAAA,CAACvB,IAAI;UAACyF,KAAK,EAAC,0BAAM;UAACoB,IAAI,EAAC,OAAO;UAAAhB,QAAA,gBAC7BtE,OAAA,CAACtB,KAAK;YACJI,OAAO,EAAC,sCAAQ;YAChByG,WAAW,EAAC,kRAAiD;YAC7DL,IAAI,EAAC,MAAM;YACXM,QAAQ;YACRV,KAAK,EAAE;cAAEG,YAAY,EAAE;YAAG;UAAE;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eAEF1E,OAAA,CAACrB,IAAI;YACHkF,IAAI,EAAEnD,kBAAmB;YACzB+E,MAAM,EAAC,UAAU;YACjBC,QAAQ,EAAExD,oBAAqB;YAC/B4C,KAAK,EAAE;cAAEa,QAAQ,EAAE;YAAI,CAAE;YAAArB,QAAA,gBAEvBtE,OAAA,CAACrB,IAAI,CAACiH,IAAI;cACRC,KAAK,EAAC,0BAAM;cACZC,IAAI,EAAC,cAAc;cACnBC,YAAY,EAAE9E,WAAY;cAAAqD,QAAA,eAE1BtE,OAAA,CAACpB,KAAK;gBACJoH,MAAM,eAAEhG,OAAA,CAACX,YAAY;kBAAAkF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACzBuB,QAAQ;gBACR5C,KAAK,EAAEpC;cAAY;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZ1E,OAAA,CAACrB,IAAI,CAACiH,IAAI;cACRC,KAAK,EAAC,oBAAK;cACXC,IAAI,EAAC,cAAc;cACnBI,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAErH,OAAO,EAAE;cAAS,CAAC,CAAE;cAAAwF,QAAA,eAE/CtE,OAAA,CAACpB,KAAK,CAACwH,QAAQ;gBACbJ,MAAM,eAAEhG,OAAA,CAACb,YAAY;kBAAAoF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACzB2B,WAAW,EAAC,sCAAQ;gBACpBC,UAAU,EAAGC,OAAO,IAAMA,OAAO,gBAAGvG,OAAA,CAACT,UAAU;kBAAAgF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAG1E,OAAA,CAACV,oBAAoB;kBAAAiF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAG;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZ1E,OAAA,CAACrB,IAAI,CAACiH,IAAI;cACRC,KAAK,EAAC,oBAAK;cACXC,IAAI,EAAC,cAAc;cACnBI,KAAK,EAAE,CAAC;gBAAEM,SAAS,EAAErD;cAAiB,CAAC,CAAE;cAAAmB,QAAA,eAEzCtE,OAAA,CAACpB,KAAK,CAACwH,QAAQ;gBACbJ,MAAM,eAAEhG,OAAA,CAACR,cAAc;kBAAA+E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC3B2B,WAAW,EAAC,sKAA+B;gBAC3CC,UAAU,EAAGC,OAAO,IAAMA,OAAO,gBAAGvG,OAAA,CAACT,UAAU;kBAAAgF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAG1E,OAAA,CAACV,oBAAoB;kBAAAiF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAG;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZ1E,OAAA,CAACrB,IAAI,CAACiH,IAAI;cACRC,KAAK,EAAC,gCAAO;cACbC,IAAI,EAAC,kBAAkB;cACvBI,KAAK,EAAE,CAAC;gBAAEM,SAAS,EAAE5C;cAAwB,CAAC,CAAE;cAAAU,QAAA,eAEhDtE,OAAA,CAACpB,KAAK,CAACwH,QAAQ;gBACbJ,MAAM,eAAEhG,OAAA,CAACR,cAAc;kBAAA+E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC3B2B,WAAW,EAAC,kDAAU;gBACtBC,UAAU,EAAGC,OAAO,IAAMA,OAAO,gBAAGvG,OAAA,CAACT,UAAU;kBAAAgF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAG1E,OAAA,CAACV,oBAAoB;kBAAAiF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAG;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEd1E,OAAA,CAACrB,IAAI,CAACiH,IAAI;cAAAtB,QAAA,eACRtE,OAAA,CAACnB,MAAM;gBACLqG,IAAI,EAAC,SAAS;gBACduB,QAAQ,EAAC,QAAQ;gBACjBnG,OAAO,EAAEA,OAAQ;gBACjBoG,IAAI,eAAE1G,OAAA,CAACb,YAAY;kBAAAoF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACvBY,IAAI,EAAC,OAAO;gBAAAhB,QAAA,EACb;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,GA5E2C,GAAG;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA6E9C,CAAC,EAGTvD,OAAO,iBACNnB,OAAA,CAACG,OAAO;QAACkF,GAAG,eAAErF,OAAA;UAAAsE,QAAA,gBAAMtE,OAAA,CAACZ,eAAe;YAAAmF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,4BAAI;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAE;QAAAJ,QAAA,eACjDtE,OAAA,CAACvB,IAAI;UAACyF,KAAK,EAAC,gCAAO;UAACoB,IAAI,EAAC,OAAO;UAAAhB,QAAA,gBAC9BtE,OAAA,CAACtB,KAAK;YACJI,OAAO,EAAC,gCAAO;YACfyG,WAAW,EAAC,4IAAyB;YACrCL,IAAI,EAAC,MAAM;YACXM,QAAQ;YACRV,KAAK,EAAE;cAAEG,YAAY,EAAE;YAAG;UAAE;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eAEF1E,OAAA,CAACrB,IAAI;YACHkF,IAAI,EAAEjD,WAAY;YAClB6E,MAAM,EAAC,UAAU;YACjBC,QAAQ,EAAE9C,aAAc;YACxBkC,KAAK,EAAE;cAAEa,QAAQ,EAAE;YAAI,CAAE;YAAArB,QAAA,gBAEvBtE,OAAA,CAACrB,IAAI,CAACiH,IAAI;cACRC,KAAK,EAAC,0BAAM;cACZC,IAAI,EAAC,cAAc;cACnBI,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAErH,OAAO,EAAE;cAAS,CAAC,EACrC;gBAAE6H,GAAG,EAAE,CAAC;gBAAE7H,OAAO,EAAE;cAAU,CAAC,EAC9B;gBAAE8H,OAAO,EAAE,iBAAiB;gBAAE9H,OAAO,EAAE;cAAmB,CAAC,CAC3D;cAAAwF,QAAA,eAEFtE,OAAA,CAACpB,KAAK;gBACJoH,MAAM,eAAEhG,OAAA,CAACX,YAAY;kBAAAkF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACzB2B,WAAW,EAAC;cAAS;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZ1E,OAAA,CAACrB,IAAI,CAACiH,IAAI;cACRC,KAAK,EAAC,gCAAO;cACbC,IAAI,EAAC,mBAAmB;cACxBI,KAAK,EAAE,CAAC;gBAAEM,SAAS,EAAErD;cAAiB,CAAC,CAAE;cAAAmB,QAAA,eAEzCtE,OAAA,CAACpB,KAAK,CAACwH,QAAQ;gBACbJ,MAAM,eAAEhG,OAAA,CAACb,YAAY;kBAAAoF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACzB2B,WAAW,EAAC,kLAAiC;gBAC7CC,UAAU,EAAGC,OAAO,IAAMA,OAAO,gBAAGvG,OAAA,CAACT,UAAU;kBAAAgF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAG1E,OAAA,CAACV,oBAAoB;kBAAAiF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAG;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZ1E,OAAA,CAACrB,IAAI,CAACiH,IAAI;cACRC,KAAK,EAAC,4CAAS;cACfC,IAAI,EAAC,uBAAuB;cAC5BI,KAAK,EAAE,CAAC;gBAAEM,SAAS,EAAE5C;cAAwB,CAAC,CAAE;cAAAU,QAAA,eAEhDtE,OAAA,CAACpB,KAAK,CAACwH,QAAQ;gBACbJ,MAAM,eAAEhG,OAAA,CAACR,cAAc;kBAAA+E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC3B2B,WAAW,EAAC,8DAAY;gBACxBC,UAAU,EAAGC,OAAO,IAAMA,OAAO,gBAAGvG,OAAA,CAACT,UAAU;kBAAAgF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAG1E,OAAA,CAACV,oBAAoB;kBAAAiF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAG;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEd1E,OAAA,CAACrB,IAAI,CAACiH,IAAI;cAAAtB,QAAA,eACRtE,OAAA,CAACnB,MAAM;gBACLqG,IAAI,EAAC,SAAS;gBACduB,QAAQ,EAAC,QAAQ;gBACjBnG,OAAO,EAAEA,OAAQ;gBACjBoG,IAAI,eAAE1G,OAAA,CAACZ,eAAe;kBAAAmF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC1BY,IAAI,EAAC,OAAO;gBAAAhB,QAAA,EACb;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,GAnE+C,GAAG;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAoElD,CACV,EAEAvD,OAAO,iBACNnB,OAAA,CAACG,OAAO;QAACkF,GAAG,eAAErF,OAAA;UAAAsE,QAAA,gBAAMtE,OAAA,CAACN,eAAe;YAAA6E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,4BAAI;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAE;QAAAJ,QAAA,eACjDtE,OAAA,CAACvB,IAAI;UACHyF,KAAK,EAAC,0BAAM;UACZoB,IAAI,EAAC,OAAO;UACZuB,KAAK,eACH7G,OAAA,CAACjB,KAAK;YAAAuF,QAAA,gBACJtE,OAAA,CAACnB,MAAM;cACLqG,IAAI,EAAC,SAAS;cACdwB,IAAI,eAAE1G,OAAA,CAACJ,cAAc;gBAAA2E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACzBoC,OAAO,EAAE1F,UAAW;cACpBd,OAAO,EAAEA,OAAQ;cAAAgE,QAAA,EAClB;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT1E,OAAA,CAACf,GAAG;cAAC2F,KAAK,EAAC,MAAM;cAAAN,QAAA,GAAE9D,KAAK,CAACiD,MAAM,EAAC,qBAAI;YAAA;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CACR;UAAAJ,QAAA,eAEDtE,OAAA,CAAChB,KAAK;YACJ+H,OAAO,EAAE9C,WAAY;YACrB+C,UAAU,EAAExG,KAAM;YAClByG,MAAM,EAAC,UAAU;YACjBC,UAAU,EAAE;cACVC,QAAQ,EAAE,EAAE;cACZC,eAAe,EAAE,IAAI;cACrBC,SAAS,EAAGC,KAAK,IAAK,KAAKA,KAAK;YAClC,CAAE;YACFhC,IAAI,EAAC;UAAO;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC,GA7B+C,GAAG;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA8BlD,CACV,EAGA,CAACvD,OAAO,iBACPnB,OAAA,CAACG,OAAO;QAACkF,GAAG,eAAErF,OAAA;UAAAsE,QAAA,gBAAMtE,OAAA,CAACP,yBAAyB;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,4BAAI;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAE;QAAAJ,QAAA,eAC3DtE,OAAA,CAACvB,IAAI;UAACyF,KAAK,EAAC,0BAAM;UAACoB,IAAI,EAAC,OAAO;UAAAhB,QAAA,eAC7BtE,OAAA,CAACtB,KAAK;YACJI,OAAO,EAAC,0BAAM;YACdyG,WAAW,EAAC,wPAA2C;YACvDL,IAAI,EAAC,SAAS;YACdM,QAAQ;YACRkB,IAAI,eAAE1G,OAAA,CAACP,yBAAyB;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC,GATyD,GAAG;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAU5D,CACV;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACrE,EAAA,CA7WID,kBAA4B;EAAA,QAGHzB,IAAI,CAACgC,OAAO,EACnBhC,IAAI,CAACgC,OAAO,EAGVd,WAAW;AAAA;AAAA0H,EAAA,GAP/BnH,kBAA4B;AA+WlC,eAAeA,kBAAkB;AAAC,IAAAmH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}