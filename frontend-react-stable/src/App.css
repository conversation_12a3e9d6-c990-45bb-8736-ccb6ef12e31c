.App {
  text-align: left;
}

/* 页面标题样式 */
.page-title {
  font-size: 20px;
  font-weight: 600;
  color: #000000;
  margin-bottom: 16px;
}

/* 统一调整所有页面的主标题大小 */
.ant-typography h2.ant-typography {
  font-size: 20px !important;
  font-weight: 600 !important;
  margin-bottom: 8px !important;
}

.page-description {
  color: #666;
  margin-bottom: 24px;
}

/* 功能卡片样式 */
.function-card {
  margin-bottom: 24px;
}

.function-card .ant-card-head-title {
  font-size: 18px;
  font-weight: 600;
}

/* 表单样式 */
.form-section {
  margin-bottom: 24px;
}

.form-section .ant-form-item {
  margin-bottom: 16px;
}

/* 按钮样式 */
.action-button {
  margin-right: 12px;
  margin-bottom: 8px;
}

/* 文件上传样式 */
.upload-area {
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  padding: 20px;
  text-align: center;
  background: #fafafa;
  transition: border-color 0.3s ease;
}

.upload-area:hover {
  border-color: #1890ff;
}

.upload-area.dragover {
  border-color: #1890ff;
  background: #e6f7ff;
}

/* 结果展示样式 */
.result-section {
  margin-top: 24px;
}

.result-card {
  margin-bottom: 16px;
}

/* 统计卡片样式 */
.stat-card {
  text-align: center;
  padding: 20px;
}

.stat-card .stat-value {
  font-size: 32px;
  font-weight: bold;
  color: #1890ff;
  display: block;
}

.stat-card .stat-label {
  font-size: 14px;
  color: #666;
  margin-top: 8px;
}

/* 进度条样式 */
.progress-section {
  margin: 20px 0;
}

/* 表格样式 */
.data-table {
  margin-top: 16px;
}

.data-table .ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
}

/* 标签页样式 */
.ant-tabs-tab {
  font-weight: 500;
}

.ant-tabs-tab.ant-tabs-tab-active {
  font-weight: 600;
}

.ant-tabs-tab.ant-tabs-tab-active .anticon {
  color: #1890ff;
}

.ant-tabs-tab .anticon {
  margin-right: 6px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .page-title {
    font-size: 20px;
  }
  
  .stat-card .stat-value {
    font-size: 24px;
  }
  
  .action-button {
    margin-bottom: 12px;
    width: 100%;
  }
}

/* 全局按钮圆角样式 */
.ant-btn {
  border-radius: 8px !important;
}

.ant-btn-sm {
  border-radius: 6px !important;
}

.ant-btn-lg {
  border-radius: 10px !important;
}

/* Input组件圆角样式 */
.ant-input {
  border-radius: 6px !important;
}

.ant-select-selector {
  border-radius: 6px !important;
}
