from fastapi import APIRouter, UploadFile, File, HTTPException, Form, Depends, BackgroundTasks # 从FastAPI框架导入所需的类和函数，用于构建API、处理文件和依赖注入
from typing import Optional, List # 从typing模块导入Optional，用于为函数参数提供可选类型提示
import pandas as pd # 导入pandas库，用于高效地处理和分析数据，特别是CSV文件
import torch # 导入PyTorch库，用于加载和运行深度学习模型
import joblib # 导入joblib库，用于加载通过scikit-learn保存的标准化器（scaler）对象
import os # 导入os模块，用于与操作系统交互，如处理文件路径和检查文件是否存在
import logging # 导入logging模块，用于在程序运行时记录信息和错误
from .model_training import GRUModel, create_sequences # 从本地的model_training模块导入之前定义的GRUModel类和辅助函数
from sklearn.metrics import r2_score # 从scikit-learn库导入r2_score函数，用于计算模型的R²（决定系数）评估指标
from collections import Counter # 从collections模块导入Counter类，用于方便地统计列表中元素的频率
import numpy as np # 导入numpy库，用于进行高性能的科学计算和数组操作
from datetime import datetime # 从datetime模块导入datetime类，用于处理和格式化日期与时间
import tempfile # 导入tempfile模块，用于安全地创建临时文件
import json # 导入json模块，用于将Python字典转换为JSON字符串以便记录日志
import time # 导入time模块，用于时间相关的操作
import psutil # 导入psutil模块，用于监控系统资源
from .auth import get_current_user # 从本地的auth模块导入get_current_user函数，用于API的用户认证
from .task_manager import task_storage, TaskStatus, TaskType # 导入任务管理器

# 尝试导入 pynvml 用于GPU监控
try:
    import pynvml
    pynvml_available = True
except ImportError:
    pynvml_available = False

router = APIRouter() # 创建一个FastAPI的APIRouter实例，用于组织和管理与模型预测相关的API路由

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s') # 设置日志记录器的基本配置，包括日志级别和输出格式

# ==================== 智能插值函数（从model_training导入） ====================
# 为了保持一致性，从model_training模块导入智能插值函数
from .model_training import optimized_interpolation_pipeline

@router.post("/predict") # 定义一个接收POST请求的/predict路由，用于执行模型预测
async def predict( # 定义一个异步的预测函数，以处理可能耗时的I/O操作
    model_filename: str = Form(...), # 修改：接收独立的模型文件名
    params_filename: str = Form(...), # 新增：接收独立的参数文件名
    scaler_filename: str = Form(...), # 新增：接收独立的标准化器文件名
    selected_prot: str = Form(...),
    selected_datatype: str = Form(...),
    file: Optional[UploadFile] = File(None), # 定义一个可选参数`file`，用于接收上传的CSV文件
    csv_dir: Optional[str] = Form(None), # 定义一个可选参数`csv_dir`，用于接收本地CSV文件的目录路径
    selected_file: Optional[str] = Form(None), # 定义一个可选参数`selected_file`，用于接收所选的本地文件名
    output_folder: str = Form("/data/output"), # 定义一个参数`output_folder`，用于接收预测结果的输出目录
    auto_generate_template: bool = Form(False), # 新增参数：是否自动生成清洗模板
    save_result_file: bool = Form(True), # 新增参数：是否保存结果文件（用于多模型预测）
    current_user: str = Depends(get_current_user) # 使用Depends进行依赖注入，以获取并验证当前登录的用户
):
    """
    使用已训练的模型进行预测，并计算动态阈值以检测异常。
    """
    # 路径构建 (基于独立的文件名)
    model_path = os.path.join(output_folder, model_filename)
    params_path = os.path.join(output_folder, params_filename)
    scaler_y_path = os.path.join(output_folder, scaler_filename)

    # 检查所有必要文件是否存在
    for path in [model_path, params_path, scaler_y_path]:
        if not os.path.exists(path):
            raise HTTPException(status_code=404, detail=f"必要文件未找到: {path}")

    # 检查数据源
    if not file and not (csv_dir and selected_file):
        raise HTTPException(status_code=400, detail="请提供CSV文件或选择本地文件！")

    temp_file_path = None
    try:
        # --- 资源监控开始 ---
        process = psutil.Process(os.getpid())
        cpu_cores = psutil.cpu_count() # 获取CPU核心数
        start_time = time.time()
        process.cpu_percent(interval=None) # 初始化CPU使用率计算

        # --- 1. 加载所有组件 ---
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        logging.info(f"用户 {current_user} 使用设备: {device}")

        # 加载模型参数，包括用于异常检测的阈值参数
        with open(params_path, 'r') as f:
            params = json.load(f)
        
        error_mean = params.get("anomaly_threshold_mean", 0)
        error_std = params.get("anomaly_threshold_std", 0)
        k_sigma = 3.0 # 3-sigma原则

        # 初始化模型
        model = GRUModel(
            input_size=params.get("input_size", 1),
            hidden_size=params["hidden_size"],
            num_layers=params["num_layers"],
            dropout=params['dropout']
        ).to(device)
        model.load_state_dict(torch.load(model_path, map_location=device, weights_only=True))
        model.eval()

        # 加载Scaler
        scaler_y = joblib.load(scaler_y_path)
        sequence_length = params['sequence_length']

        # --- 2. 加载和预处理新数据 ---
        # 统一处理上传文件和本地文件
        csv_path = ""
        if file and file.filename:
            with tempfile.NamedTemporaryFile(delete=False, suffix=".csv") as temp_file:
                temp_file_path = temp_file.name
                temp_file.write(await file.read())
            csv_path = temp_file_path
        elif csv_dir and selected_file:
            csv_path = os.path.join(csv_dir, selected_file)
        
        if not os.path.exists(csv_path):
            raise HTTPException(status_code=404, detail=f"文件未找到: {csv_path}")
        
        df = pd.read_csv(csv_path, na_values=['', 'NA'], keep_default_na=True, low_memory=False)

        # --- 新增：使用与训练时完全相同的逻辑来筛选和预处理数据 ---
        filter_config = {
            "TCP": {
                "spt_sip_dip": {'groupby_keys': ['srcaddress', 'dstaddress', 'srcport']},
                "dpt_sip_dip": {'groupby_keys': ['srcaddress', 'dstaddress', 'dstport']},
                "len_dpt_syn": {'groupby_keys': ['dstaddress', 'dstport'], 'pre_filter': lambda d: d[d['tcpflags'] == 2]},
                "seq_ack_dip": {'groupby_keys': ['dstaddress', 'dstport'], 'pre_filter': lambda d: d[d['tcpflags'] == 16]}
            },
            "UDP": {
                "spt_sip_dip": {'groupby_keys': ['srcaddress', 'dstaddress', 'srcport']},
                "dpt_sip_dip": {'groupby_keys': ['srcaddress', 'dstaddress', 'dstport']}
            },
            "ICMP": {
                "dip": {'groupby_keys': ['dstaddress']}
            }
        }
        
        # 1. 根据协议筛选
        df_prot_filtered = df[df['protocol'] == selected_prot]
        if df_prot_filtered.empty:
            raise HTTPException(status_code=400, detail=f"在文件中找不到协议为 {selected_prot} 的数据。")
        
        # 2. 根据数据类型获取配置
        prot_config = filter_config.get(selected_prot, {})
        datatype_config = prot_config.get(selected_datatype)
        if not datatype_config:
            raise HTTPException(status_code=400, detail=f"无效的协议和数据类型组合: {selected_prot} - {selected_datatype}")

        # 3. 应用数据类型的特定过滤器
        df_data = df_prot_filtered
        if 'pre_filter' in datatype_config:
            if 'tcpflags' not in df_data.columns:
                 raise HTTPException(status_code=400, detail=f"数据缺少 'tcpflags' 列，无法应用 {selected_datatype} 筛选器。")
            df_data = datatype_config['pre_filter'](df_data)
        
        groupby_keys = datatype_config['groupby_keys']
        for key in groupby_keys:
            if key not in df_data.columns:
                raise HTTPException(status_code=400, detail=f"数据缺少 '{key}' 列，无法进行分组。")

        if not df_data.empty:
            # 修改逻辑：只保留记录数最多的组
            # 计算每个分组的大小
            group_counts = df_data.groupby(groupby_keys).size()

            # 找到记录数最多的组
            max_count = group_counts.max()
            largest_groups = group_counts[group_counts == max_count].index

            # 如果有多个组都是最大记录数，选择第一个
            if len(largest_groups) > 1:
                largest_group = largest_groups[0]
                logging.info(f"预测 {selected_prot} - {selected_datatype} 发现 {len(largest_groups)} 个最大组（记录数={max_count}），选择第一个: {largest_group}")
            else:
                largest_group = largest_groups[0]
                logging.info(f"预测 {selected_prot} - {selected_datatype} 选择最大组（记录数={max_count}）: {largest_group}")

            # 只保留最大组的数据
            if isinstance(largest_group, tuple):
                # 多列分组的情况
                mask = True
                for i, key in enumerate(groupby_keys):
                    mask = mask & (df_data[key] == largest_group[i])
                df_data = df_data[mask]
            else:
                # 单列分组的情况
                df_data = df_data[df_data[groupby_keys[0]] == largest_group]
        
        if df_data.empty:
            raise HTTPException(status_code=400, detail=f"根据选择的协议 ({selected_prot}) 和数据类型 ({selected_datatype}) 筛选后，没有剩余数据可用于预测。")

        df = df_data.copy() # 使用完全筛选后的数据

        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s')
        df = df.sort_values('timestamp')
        
        resampled = df.resample('120s', on='timestamp').agg(
            total_packetssam=('packetssam', 'sum'),
            count=('packetssam', 'count')
        ) # 添加count列以支持智能插值
        resampled['packets_per_sec'] = resampled['total_packetssam'] / 960

        # --- 详细调试：分析插值前的数据状况 ---
        total_points = len(resampled)
        missing_points = resampled['packets_per_sec'].isna().sum()
        non_zero_points = (resampled['packets_per_sec'] > 0).sum()
        zero_points = (resampled['packets_per_sec'] == 0).sum()

        logging.info(f"重采样后数据分析:")
        logging.info(f"  总数据点: {total_points}")
        logging.info(f"  缺失数据点(NaN): {missing_points}")
        logging.info(f"  非零数据点: {non_zero_points}")
        logging.info(f"  零值数据点: {zero_points}")

        # 记录前几个和后几个非NaN值
        non_nan_data = resampled['packets_per_sec'].dropna()
        if len(non_nan_data) > 0:
            logging.info(f"  前5个非NaN值: {non_nan_data.head().tolist()}")
            logging.info(f"  后5个非NaN值: {non_nan_data.tail().tolist()}")
            logging.info(f"  非NaN值的统计: min={non_nan_data.min():.2f}, max={non_nan_data.max():.2f}, mean={non_nan_data.mean():.2f}")

        # 检查时间间隔
        time_diff = resampled.index.to_series().diff()
        max_gap = time_diff.max()
        logging.info(f"  最大时间间隔: {max_gap}")

        # --- 使用智能插值策略，区分真实零值和缺失值 ---
        resampled = optimized_interpolation_pipeline(resampled)

        remaining_missing = resampled['packets_per_sec'].isna().sum()
        logging.info(f"  最终剩余缺失数据点: {remaining_missing}")

        smoothing_window = 5
        nf_data = resampled.reset_index().copy()
        nf_data['packets_per_sec_smooth'] = nf_data['packets_per_sec'].rolling(window=smoothing_window, min_periods=1).mean()
        
        nf_data['packets_per_sec_log'] = np.log1p(nf_data['packets_per_sec_smooth'])
        nf_data['packets_per_sec_log_diff'] = nf_data['packets_per_sec_log'].diff().fillna(0)
        
        target_col = 'packets_per_sec_log_diff'
        
        if len(nf_data) < sequence_length:
             raise HTTPException(status_code=400, detail="数据不足，无法生成任何预测序列")

        X_seq, _ = create_sequences(nf_data[[target_col]].values, nf_data[target_col].values, sequence_length)

        if X_seq.shape[0] == 0:
            raise HTTPException(status_code=400, detail="数据处理后不足以生成预测序列")

        # 修改：将 NumPy 数组转换为 DataFrame 并设置列名，以匹配训练时的格式
        X_seq_df = pd.DataFrame(X_seq.reshape(-1, 1), columns=['packets_per_sec_log_diff'])
        X_seq_scaled = scaler_y.transform(X_seq_df).reshape(X_seq.shape)
        X_tensor = torch.tensor(X_seq_scaled, dtype=torch.float32).to(device)

        # --- 3. 模型预测与逆转换 ---
        with torch.no_grad():
            predictions_scaled = model(X_tensor).cpu().numpy().flatten()
        
        # 修改：将预测结果转换为 DataFrame 并设置列名，以匹配训练时的格式
        predictions_scaled_df = pd.DataFrame(predictions_scaled.reshape(-1, 1), columns=['packets_per_sec_log_diff'])
        predictions_diff = scaler_y.inverse_transform(predictions_scaled_df).flatten()
        
        last_log_values_start_index = len(nf_data) - len(predictions_diff) - 1
        last_log_values = nf_data['packets_per_sec_log'].iloc[last_log_values_start_index:-1].values

        min_len = min(len(predictions_diff), len(last_log_values))
        predictions_diff = predictions_diff[:min_len]
        last_log_values = last_log_values[:min_len]
        predicted_log_values = last_log_values + predictions_diff
        
        predicted_smooth_values = np.expm1(predicted_log_values)

        # --- 4. 计算动态阈值并准备输出 ---
        dynamic_thresholds = predicted_smooth_values + error_mean + k_sigma * error_std

        # --- 新增: 计算一个总的建议阈值 ---
        # 这个阈值代表了在当前数据集上，模型预测的平均流量趋势加上一个代表正常波动的误差范围。
        # 它可以被视为一个静态的、适用于整个数据集的清洗阈值参考。
        if len(predicted_smooth_values) > 0:
            suggested_threshold = float(np.mean(predicted_smooth_values) + error_mean + k_sigma * error_std)
        else:
            # 如果没有预测值，则基于训练时的误差给出一个基础阈值
            suggested_threshold = float(error_mean + k_sigma * error_std)
            
        # --- 修改: 将计算出的阈值更新到现有的 _results.txt 文件 ---
        # 基于输入的CSV文件名称构建结果文件名
        if file and file.filename:
            input_filename = os.path.splitext(file.filename)[0]
        elif selected_file:
            input_filename = os.path.splitext(selected_file)[0]
        else:
            # 如果没有有效的输入文件名，则使用模型名称作为备选
            input_filename = model_filename.replace('_model_best.pth', '')
        
        result_filename = f"{input_filename}_results.txt"
        result_path = os.path.join(output_folder, result_filename)
        
        try:
            # 将阈值四舍五入为整数
            threshold_to_write = int(round(suggested_threshold))
            
            # 检查结果文件是否存在
            if not os.path.exists(result_path):
                logging.warning(f"结果文件 {result_path} 不存在，将创建新文件。")
                updated_results = []
            else:
                # 读取现有的结果文件
                with open(result_path, 'r', encoding='utf-8') as f:
                    updated_results = f.readlines()
                
            # 构造新的结果行
            new_result = f"{selected_prot} {selected_datatype} {threshold_to_write}\n"
            
            # 检查是否需要更新现有行
            replaced = False
            for i, line in enumerate(updated_results):
                parts = line.strip().split()
                if len(parts) >= 2 and parts[0] == selected_prot and parts[1] == selected_datatype:
                    updated_results[i] = new_result
                    replaced = True
                    logging.info(f"已更新结果文件中的阈值: {selected_prot} {selected_datatype} {threshold_to_write}")
                    break
            
            # 如果没有找到匹配的行，则添加新行
            if not replaced:
                updated_results.append(new_result)
                logging.info(f"已在结果文件中添加新阈值: {selected_prot} {selected_datatype} {threshold_to_write}")
            
            # 写回文件
            with open(result_path, 'w', encoding='utf-8') as f:
                f.writelines(updated_results)
                
            logging.info(f"阈值已写入到文件 {result_path}")
        except Exception as e:
            logging.error(f"更新阈值到结果文件失败: {e}")
            # 不抛出异常，继续执行，因为这不是关键功能

        output_start_index = len(nf_data) - len(predicted_smooth_values)
        pred_df = nf_data.iloc[output_start_index:].copy()
        
        pred_df['pred_smooth'] = predicted_smooth_values
        pred_df['threshold'] = dynamic_thresholds
        # 判断每一行是否为异常
        pred_df['is_anomaly'] = pred_df['packets_per_sec_smooth'] > pred_df['threshold']

        # --- 新增: 将数值结果四舍五入为整数 ---
        pred_df['packets_per_sec'] = pred_df['packets_per_sec'].round().astype(int)
        pred_df['packets_per_sec_smooth'] = pred_df['packets_per_sec_smooth'].round().astype(int)
        pred_df['pred_smooth'] = pred_df['pred_smooth'].round().astype(int)
        pred_df['threshold'] = pred_df['threshold'].round().astype(int)

        # 将 timestamp 转换为字符串以便 JSON 序列化
        pred_df['timestamp'] = pred_df['timestamp'].dt.strftime('%Y-%m-%d %H:%M:%S')

        output_data = pred_df[['timestamp', 'packets_per_sec', 'packets_per_sec_smooth', 'pred_smooth', 'threshold', 'is_anomaly']].to_dict('records')
        
        anomaly_count = int(pred_df['is_anomaly'].sum())
        
        model_name_display = model_filename.replace('_model_best.pth', '')

        # --- 资源监控结束 ---
        duration = time.time() - start_time
        cpu_usage = process.cpu_percent(interval=None)
        # --- 修改: 将CPU使用率转换为单核标准化值 (占总体资源的百分比) ---
        cpu_usage_normalized = cpu_usage / cpu_cores if cpu_cores else cpu_usage
        memory_mb = process.memory_info().rss / (1024 * 1024)

        # --- 新增: GPU资源监控 ---
        gpu_memory_mb = 0
        gpu_utilization_percent = 0
        if pynvml_available and device.type == 'cuda':
            try:
                pynvml.nvmlInit()
                handle = pynvml.nvmlDeviceGetHandleByIndex(0) # 预测只在device 0上进行
                mem_info = pynvml.nvmlDeviceGetMemoryInfo(handle)
                util_rates = pynvml.nvmlDeviceGetUtilizationRates(handle)
                gpu_memory_mb = mem_info.used / (1024 * 1024)
                gpu_utilization_percent = util_rates.gpu
                pynvml.nvmlShutdown()
            except pynvml.NVMLError:
                pass # 如果GPU监控失败，则静默处理

        # 保存预测结果到文件（用于生成清洗模板）
        result_path = None
        if save_result_file:
            try:
                # 构建结果文件路径 - 使用日期而不是时间戳，便于多模型共享同一文件
                from datetime import datetime
                date_str = datetime.now().strftime("%Y%m%d")
                csv_base_name = os.path.splitext(os.path.basename(csv_path))[0] if csv_path else "prediction"
                result_filename = f"{csv_base_name}_{date_str}_results.txt"
                result_path = os.path.join(output_folder, result_filename)

                # 确保输出目录存在
                os.makedirs(output_folder, exist_ok=True)

                # 将阈值写入结果文件
                threshold_to_write = int(np.round(suggested_threshold))
                result_line = f"{selected_prot} {selected_datatype} {threshold_to_write}\n"

                # 检查是否已存在结果文件，如果存在则追加而不是覆盖
                if os.path.exists(result_path):
                    # 读取现有内容
                    with open(result_path, 'r', encoding='utf-8') as f:
                        existing_lines = f.readlines()

                    # 检查是否已存在相同的协议和数据类型组合
                    updated = False
                    for i, line in enumerate(existing_lines):
                        parts = line.strip().split()
                        if len(parts) >= 2 and parts[0] == selected_prot and parts[1] == selected_datatype:
                            existing_lines[i] = result_line
                            updated = True
                            break

                    if not updated:
                        existing_lines.append(result_line)

                    # 写回文件
                    with open(result_path, 'w', encoding='utf-8') as f:
                        f.writelines(existing_lines)
                else:
                    # 创建新文件
                    with open(result_path, 'w', encoding='utf-8') as f:
                        f.write(result_line)

                logging.info(f"预测结果已保存到: {result_path}")

            except Exception as e:
                logging.warning(f"保存预测结果文件失败: {e}")
                result_path = None

        # 自动生成清洗模板（如果用户选择了该选项）
        template_info = None
        if auto_generate_template:
            # 如果没有保存结果文件，需要先找到或创建结果文件
            if not result_path:
                from datetime import datetime
                date_str = datetime.now().strftime("%Y%m%d")
                csv_base_name = os.path.splitext(os.path.basename(csv_path))[0] if csv_path else "prediction"
                result_filename = f"{csv_base_name}_{date_str}_results.txt"
                result_path = os.path.join(output_folder, result_filename)

            if result_path and os.path.exists(result_path):
                try:
                    from .clean_template import generate_clean_template

                    # 调用清洗模板生成功能
                    template_result = await generate_clean_template(
                        results_file=result_path,
                        output_folder=output_folder,
                        template_name=None,  # 使用默认命名规则
                        current_user=current_user
                    )

                    template_info = {
                        "template_generated": True,
                        "template_path": template_result.get("template_path"),
                        "updated_thresholds": template_result.get("updated_thresholds")
                    }
                    logging.info(f"用户 {current_user}: 自动生成清洗模板成功 - {template_result.get('template_path')}")

                except Exception as template_error:
                    logging.warning(f"用户 {current_user}: 自动生成清洗模板失败: {template_error}")
                    template_info = {
                        "template_generated": False,
                        "error": str(template_error)
                    }

        # 构建返回结果
        result = {
            "predictions": output_data,
            "anomaly_count": anomaly_count,
            "suggested_threshold": suggested_threshold,
            "model_name": model_name_display,
            "message": "预测成功",
            "duration_seconds": duration,
            "cpu_percent": cpu_usage_normalized,
            "memory_mb": memory_mb,
            "gpu_memory_mb": gpu_memory_mb,
            "gpu_utilization_percent": gpu_utilization_percent
        }

        if template_info:
            result["template_info"] = template_info
        if result_path:
            result["result_path"] = result_path

        return result

    except Exception as e:
        logging.error(f"预测失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"预测过程中发生严重错误: {str(e)}")
    finally:
        if temp_file_path and os.path.exists(temp_file_path):
            os.remove(temp_file_path)

@router.get("/list_model_files")
async def list_model_files(model_dir: str = "/data/output", current_user: str = Depends(get_current_user)):
    if not os.path.isdir(model_dir):
        raise HTTPException(status_code=400, detail="模型目录路径不存在！")
    
    files = os.listdir(model_dir)
    # 分别查找并排序不同类型的文件
    pth_files = sorted([f for f in files if f.endswith('_model_best.pth')])
    pkl_files = sorted([f for f in files if f.endswith('_scaler_y_best.pkl')])
    json_files = sorted([f for f in files if f.endswith('_params.json')])
    
    return {"pth_files": pth_files, "pkl_files": pkl_files, "json_files": json_files}

@router.get("/list_csv_files") # 定义一个GET请求的/list_csv_files路由，用于列出可用于预测的CSV文件
async def list_csv_files(csv_dir: str = "/data/output", current_user: str = Depends(get_current_user)): # 定义一个异步函数来列出CSV文件
    if not os.path.exists(csv_dir): # 检查指定的CSV目录是否存在
        raise HTTPException(status_code=400, detail="CSV目录路径不存在！") # 如果不存在，则返回400错误
    # 修改：同时过滤掉 _test.csv 文件
    csv_files = [f for f in os.listdir(csv_dir) if f.endswith('.csv') and not f.endswith('_test.csv') and not f.endswith('_predictions.csv')] # 筛选出所有非预测结果的CSV文件
    csv_files.sort() # 对文件列表进行排序
    return {"files": csv_files} # 以JSON格式返回文件列表

@router.get("/get_matching_files")
async def get_matching_files(model_filename: str, model_dir: str = "/data/output", current_user: str = Depends(get_current_user)):
    """
    根据选择的模型文件自动获取匹配的参数文件和标准化器文件
    """
    if not os.path.isdir(model_dir):
        raise HTTPException(status_code=400, detail="模型目录路径不存在！")
    
    if not model_filename.endswith('_model_best.pth'):
        raise HTTPException(status_code=400, detail="无效的模型文件名格式！")
    
    # 从模型文件名中提取基本名称
    base_name = model_filename.replace('_model_best.pth', '')
    
    # 构建匹配的参数文件和标准化器文件名
    params_filename = f"{base_name}_params.json"
    scaler_filename = f"{base_name}_scaler_y_best.pkl"
    
    # 检查文件是否存在
    params_path = os.path.join(model_dir, params_filename)
    scaler_path = os.path.join(model_dir, scaler_filename)
    
    if not os.path.exists(params_path):
        raise HTTPException(status_code=404, detail=f"匹配的参数文件未找到: {params_filename}")
    
    if not os.path.exists(scaler_path):
        raise HTTPException(status_code=404, detail=f"匹配的标准化器文件未找到: {scaler_filename}")
    
    # 从文件名中提取协议和数据类型信息
    parts = base_name.split('_')
    protocol = None
    datatype = None
    
    # 尝试识别协议和数据类型
    protocols = ["TCP", "UDP", "ICMP"]
    datatypes = {
        "TCP": ["spt_sip_dip", "dpt_sip_dip", "len_dpt_syn", "seq_ack_dip"],
        "UDP": ["spt_sip_dip", "dpt_sip_dip"],
        "ICMP": ["dip"]
    }
    
    # 查找协议
    for p in protocols:
        if p in parts:
            protocol = p
            # 找到协议后，查找该协议的可能数据类型
            if protocol in datatypes:
                for dt in datatypes[protocol]:
                    # 检查数据类型是否在文件名中
                    if dt in base_name:
                        datatype = dt
                        break
            break
    
    return {
        "params_filename": params_filename,
        "scaler_filename": scaler_filename,
        "protocol": protocol,
        "datatype": datatype
    }

# 异步预测接口
@router.post("/predict_async")
async def predict_async(
    background_tasks: BackgroundTasks,
    model_filename: str = Form(...),
    params_filename: str = Form(...),
    scaler_filename: str = Form(...),
    selected_prot: str = Form(...),
    selected_datatype: str = Form(...),
    file: UploadFile = File(None),
    csv_dir: Optional[str] = Form(None),
    selected_file: Optional[str] = Form(None),
    model_dir: str = Form("/data/output"),
    output_folder: str = Form("/data/output"),
    auto_generate_template: bool = Form(False), # 新增参数：是否自动生成清洗模板
    save_result_file: bool = Form(True), # 新增参数：是否保存结果文件（用于多模型预测）
    current_user: str = Depends(get_current_user)
):
    """异步启动模型预测任务"""
    # 验证输入
    if not file and not (csv_dir and selected_file):
        raise HTTPException(status_code=400, detail="请提供CSV文件或选择本地文件！")

    # 准备文件内容
    file_content = None
    csv_filename = "unknown"

    if file and file.filename:
        csv_filename = file.filename.split(".")[0]
        file_content = await file.read()
    else:
        csv_filename = selected_file.split(".")[0] if selected_file else "unknown"

    # 创建任务
    task_params = {
        "model_filename": model_filename,
        "params_filename": params_filename,
        "scaler_filename": scaler_filename,
        "selected_prot": selected_prot,
        "selected_datatype": selected_datatype,
        "csv_filename": csv_filename,
        "model_dir": model_dir,
        "output_folder": output_folder,
        "auto_generate_template": auto_generate_template,
        "save_result_file": save_result_file,
        "user": current_user
    }

    # 使用CSV文件名作为任务ID前缀
    task_id = task_storage.create_task(TaskType.PREDICTION, task_params, prefix=csv_filename)

    # 临时解决方案：直接同步执行预测任务
    logging.info(f"用户 {current_user} 启动了异步预测任务 {task_id} (临时同步执行)")

    # 在新线程中执行预测任务
    import threading

    def run_in_thread():
        predict_model_background(
            task_id, file_content, csv_filename, csv_dir, selected_file,
            model_filename, params_filename, scaler_filename,
            selected_prot, selected_datatype, model_dir, output_folder, auto_generate_template, save_result_file, current_user
        )

    # 启动线程
    thread = threading.Thread(target=run_in_thread)
    thread.daemon = True
    thread.start()

    logging.info(f"预测任务 {task_id} 已在新线程中启动")

    return {
        "success": True,
        "task_id": task_id,
        "message": "预测任务已启动，请通过任务ID查询进度"
    }

# 多模型异步预测接口
@router.post("/predict_multi_async")
async def predict_multi_async(
    background_tasks: BackgroundTasks,
    models_config: str = Form(...),  # JSON字符串，包含多个模型的配置
    file: UploadFile = File(None),
    csv_dir: Optional[str] = Form(None),
    selected_file: Optional[str] = Form(None),
    model_dir: str = Form("/data/output"),
    output_folder: str = Form("/data/output"),
    auto_generate_template: bool = Form(False),
    save_result_file: bool = Form(True),
    current_user: str = Depends(get_current_user)
):
    """异步启动多模型预测任务"""
    import json

    # 验证输入
    if not file and not (csv_dir and selected_file):
        raise HTTPException(status_code=400, detail="请提供CSV文件或选择本地文件！")

    # 解析模型配置
    try:
        models_list = json.loads(models_config)
        if not models_list or not isinstance(models_list, list):
            raise HTTPException(status_code=400, detail="模型配置格式错误")
    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="模型配置JSON格式错误")

    # 准备文件内容
    file_content = None
    csv_filename = "unknown"

    if file and file.filename:
        csv_filename = file.filename.split(".")[0]
        file_content = await file.read()
    else:
        csv_filename = selected_file.split(".")[0] if selected_file else "unknown"

    # 创建任务
    task_params = {
        "models_config": models_list,
        "csv_filename": csv_filename,
        "model_dir": model_dir,
        "output_folder": output_folder,
        "auto_generate_template": auto_generate_template,
        "save_result_file": save_result_file,
        "user": current_user
    }

    # 使用CSV文件名作为任务ID前缀（与单模型保持一致）
    task_id = task_storage.create_task(TaskType.PREDICTION, task_params, prefix=csv_filename)

    logging.info(f"用户 {current_user} 启动了多模型异步预测任务 {task_id}")

    # 在新线程中执行多模型预测任务
    import threading

    def run_in_thread():
        predict_multi_models_background(
            task_id, file_content, csv_filename, csv_dir, selected_file,
            models_list, model_dir, output_folder, auto_generate_template, save_result_file, current_user
        )

    # 启动线程
    thread = threading.Thread(target=run_in_thread)
    thread.daemon = True
    thread.start()

    logging.info(f"多模型预测任务 {task_id} 已在新线程中启动")

    return {
        "success": True,
        "task_id": task_id,
        "message": "多模型预测任务已启动，请通过任务ID查询进度"
    }

# 后台预测函数
def predict_model_background(
    task_id: str,
    file_content: bytes,
    csv_filename: str,
    csv_dir: str,
    selected_file: str,
    model_filename: str,
    params_filename: str,
    scaler_filename: str,
    selected_prot: str,
    selected_datatype: str,
    model_dir: str,
    output_folder: str,
    auto_generate_template: bool,
    save_result_file: bool,
    current_user: str
):
    """后台执行模型预测的函数"""

    try:
        # 更新任务状态为运行中
        task_storage.update_task_status(task_id, TaskStatus.RUNNING, current_step="初始化预测环境")

        # 检查设备
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        logging.info(f"任务 {task_id}: 用户 {current_user} 使用设备: {device}")

        # 更新进度
        task_storage.update_task_progress(task_id, 10, "准备数据文件")

        # 处理文件
        temp_file_path = None
        csv_path = None

        if file_content:
            # 处理上传的文件
            with tempfile.NamedTemporaryFile(delete=False, suffix=".csv") as temp_file:
                temp_file_path = temp_file.name
                temp_file.write(file_content)
            csv_path = temp_file_path
        else:
            # 处理本地文件
            csv_path = os.path.join(csv_dir, selected_file)
            csv_filename = selected_file.split(".")[0]

        # 构建文件路径
        model_path = os.path.join(model_dir, model_filename)
        params_path = os.path.join(model_dir, params_filename)
        scaler_path = os.path.join(model_dir, scaler_filename)

        # 读取CSV文件
        df = pd.read_csv(csv_path)
        logging.info(f"任务 {task_id}: 成功读取CSV文件，形状: {df.shape}")

        # 调用预测函数
        result = _predict_single_model(
            task_id, df, model_path, params_path, scaler_path,
            selected_prot, selected_datatype, output_folder, csv_filename, device, auto_generate_template, save_result_file, current_user
        )

        # 清理临时文件
        if temp_file_path and os.path.exists(temp_file_path):
            os.remove(temp_file_path)

        # 更新任务状态为完成
        task_storage.update_task_status(task_id, TaskStatus.COMPLETED)

        # 保存结果
        task_storage.set_task_result(task_id, result)

        logging.info(f"任务 {task_id}: 预测完成")

    except Exception as e:
        logging.error(f"任务 {task_id}: 预测失败: {e}")
        task_storage.update_task_status(task_id, TaskStatus.FAILED, error=str(e))

# 多模型后台预测函数
def predict_multi_models_background(
    task_id: str,
    file_content: bytes,
    csv_filename: str,
    csv_dir: str,
    selected_file: str,
    models_list: list,
    model_dir: str,
    output_folder: str,
    auto_generate_template: bool,
    save_result_file: bool,
    current_user: str
):
    """后台执行多模型预测的函数"""
    import tempfile
    import torch

    try:
        # 更新任务状态
        task_storage.update_task_status(task_id, TaskStatus.RUNNING)
        task_storage.update_task_progress(task_id, 5, "初始化多模型预测")

        # 检查GPU可用性
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        logging.info(f"任务 {task_id}: 使用设备: {device}")

        # 准备CSV文件
        temp_file_path = None
        if file_content:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(mode='wb', suffix='.csv', delete=False) as temp_file:
                temp_file.write(file_content)
                temp_file_path = temp_file.name
            csv_path = temp_file_path
        else:
            csv_path = os.path.join(csv_dir, selected_file)

        if not os.path.exists(csv_path):
            raise Exception(f"CSV文件不存在: {csv_path}")

        # 读取CSV文件
        df = pd.read_csv(csv_path)
        logging.info(f"任务 {task_id}: 成功读取CSV文件，形状: {df.shape}")

        task_storage.update_task_progress(task_id, 10, "开始多模型预测")

        # 存储所有模型的预测结果
        all_results = []
        total_models = len(models_list)

        for i, model_config in enumerate(models_list):
            try:
                model_name = f"{model_config['protocol']}_{model_config['datatype']}"
                current_step = f"预测模型 {i+1}/{total_models}: {model_name}"
                progress = 10 + int((i / total_models) * 70)  # 10-80% 用于模型预测

                task_storage.update_task_progress(task_id, progress, current_step)
                logging.info(f"任务 {task_id}: {current_step}")

                # 调用单模型预测函数
                model_result = _predict_single_model(
                    f"{task_id}_model_{i+1}", df,
                    os.path.join(model_dir, model_config['model_file']),
                    os.path.join(model_dir, model_config['params_file']),
                    os.path.join(model_dir, model_config['scaler_file']),
                    model_config['protocol'], model_config['datatype'],
                    output_folder, csv_filename, device, False, False, current_user  # 单个模型不生成模板和保存文件
                )

                # 添加模型标识信息
                model_result['model_info'] = {
                    'model_file': model_config['model_file'],
                    'protocol': model_config['protocol'],
                    'datatype': model_config['datatype'],
                    'model_name': model_name
                }

                all_results.append(model_result)
                logging.info(f"任务 {task_id}: 模型 {model_name} 预测完成")

            except Exception as model_error:
                logging.error(f"任务 {task_id}: 模型 {model_config} 预测失败: {model_error}")
                # 记录失败的模型，但继续处理其他模型
                error_result = {
                    'model_info': {
                        'model_file': model_config.get('model_file', 'unknown'),
                        'protocol': model_config.get('protocol', 'unknown'),
                        'datatype': model_config.get('datatype', 'unknown'),
                        'model_name': f"{model_config.get('protocol', 'unknown')}_{model_config.get('datatype', 'unknown')}"
                    },
                    'error': str(model_error),
                    'success': False
                }
                all_results.append(error_result)

        # 汇总结果
        task_storage.update_task_progress(task_id, 85, "汇总多模型预测结果")

        successful_results = [r for r in all_results if not r.get('error')]
        failed_results = [r for r in all_results if r.get('error')]

        # 构建最终结果 - 与同步多模型预测保持一致的格式
        # 将每个模型的结果转换为前端期望的格式
        formatted_results = []
        for result in all_results:
            if not result.get('error'):
                # 成功的模型结果
                model_info = result.get('model_info', {})
                formatted_result = {
                    'model_name': model_info.get('model_name', 'unknown'),
                    'suggested_threshold': result.get('suggested_threshold', 0),
                    'message': result.get('message', '预测完成'),
                    'duration_seconds': result.get('duration_seconds'),
                    'cpu_percent': result.get('cpu_percent'),
                    'memory_mb': result.get('memory_mb'),
                    'gpu_memory_mb': result.get('gpu_memory_mb'),
                    'gpu_utilization_percent': result.get('gpu_utilization_percent'),
                    'anomaly_count': result.get('anomaly_count', 0)
                }
                formatted_results.append(formatted_result)
            else:
                # 失败的模型结果
                model_info = result.get('model_info', {})
                formatted_result = {
                    'model_name': model_info.get('model_name', 'unknown'),
                    'suggested_threshold': 0,
                    'message': f"预测失败: {result.get('error', '未知错误')}",
                    'error': result.get('error'),
                    'anomaly_count': 0
                }
                formatted_results.append(formatted_result)

        # 构建与同步预测一致的最终结果
        final_result = {
            # 主要结果数组 - 与同步预测的 results 数组格式一致
            'results': formatted_results,
            # 多模型标识
            'is_multi_model': True,
            'total_models': total_models,
            'successful_models': len(successful_results),
            'failed_models': len(failed_results),
            # 汇总信息
            'summary': {
                'total_models': total_models,
                'successful_models': len(successful_results),
                'failed_models': len(failed_results),
                'success_rate': len(successful_results) / total_models if total_models > 0 else 0
            },
            'csv_filename': csv_filename,
            'execution_info': {
                'device': str(device),
                'total_predictions': sum(len(r.get('predictions', [])) for r in successful_results),
                'total_anomalies': sum(r.get('anomaly_count', 0) for r in successful_results)
            }
        }

        # 添加模板信息
        if successful_results:
            # 查找是否有模板信息
            for result in successful_results:
                if result.get('template_info'):
                    final_result['template_info'] = result['template_info']
                    break

        # 处理自动生成清洗模板和保存结果文件
        if successful_results and (auto_generate_template or save_result_file):
            task_storage.update_task_progress(task_id, 90, "处理后续任务")

            # 使用第一个成功的结果来生成模板和保存文件
            first_success = successful_results[0]

            if save_result_file:
                try:
                    # 构建结果文件路径 - 与单模型异步预测保持一致的命名
                    from datetime import datetime
                    date_str = datetime.now().strftime("%Y%m%d")
                    result_filename = f"{csv_filename}_{date_str}_results.txt"
                    result_path = os.path.join(output_folder, result_filename)

                    # 保存多模型汇总结果 - 兼容清洗模板格式
                    with open(result_path, 'w', encoding='utf-8') as f:
                        # 首先写入清洗模板需要的格式（协议 数据类型 阈值）
                        for result in successful_results:
                            model_info = result.get('model_info', {})
                            protocol = model_info.get('protocol', 'unknown')
                            datatype = model_info.get('datatype', 'unknown')
                            threshold = result.get('suggested_threshold', 0)
                            # 将阈值四舍五入为整数
                            threshold_int = int(round(float(threshold))) if threshold != 'N/A' else 0
                            f.write(f"{protocol} {datatype} {threshold_int}\n")

                        # 然后写入详细的汇总信息（用注释符号开头，避免被清洗模板解析）
                        f.write(f"\n# 多模型预测结果汇总\n")
                        f.write(f"# CSV文件: {csv_filename}\n")
                        f.write(f"# 预测时间: {datetime.now().strftime('%Y-%m-%d_%H-%M-%S')}\n")  # 修改时间格式，避免空格和冒号
                        f.write(f"# 总模型数: {total_models}\n")
                        f.write(f"# 成功模型数: {len(successful_results)}\n")
                        f.write(f"# 失败模型数: {len(failed_results)}\n\n")

                        for i, result in enumerate(all_results):
                            model_info = result.get('model_info', {})
                            f.write(f"# 模型 {i+1}: {model_info.get('model_name', 'unknown')}\n")
                            if result.get('error'):
                                f.write(f"#   状态: 失败\n")
                                f.write(f"#   错误: {result['error']}\n")
                            else:
                                f.write(f"#   状态: 成功\n")
                                f.write(f"#   建议阈值: {result.get('suggested_threshold', 'N/A')}\n")
                                f.write(f"#   异常数量: {result.get('anomaly_count', 'N/A')}\n")
                            f.write("#\n")

                    final_result['result_file_path'] = result_path
                    logging.info(f"任务 {task_id}: 多模型结果文件已保存到 {result_path}")

                except Exception as save_error:
                    logging.warning(f"任务 {task_id}: 保存结果文件失败: {save_error}")

            if auto_generate_template and result_path:
                try:
                    from .clean_template import generate_clean_template
                    import asyncio

                    # 在同步函数中调用异步函数
                    try:
                        loop = asyncio.get_event_loop()
                        template_result = loop.run_until_complete(generate_clean_template(
                            results_file=result_path,
                            output_folder=output_folder,
                            template_name=None,
                            current_user=current_user
                        ))
                    except RuntimeError:
                        template_result = asyncio.run(generate_clean_template(
                            results_file=result_path,
                            output_folder=output_folder,
                            template_name=None,
                            current_user=current_user
                        ))

                    final_result['template_info'] = {
                        "template_generated": True,
                        "template_path": template_result.get("template_path"),
                        "updated_thresholds": template_result.get("updated_thresholds")
                    }
                    logging.info(f"任务 {task_id}: 自动生成清洗模板成功")

                except Exception as template_error:
                    logging.warning(f"任务 {task_id}: 自动生成清洗模板失败: {template_error}")
                    final_result['template_info'] = {
                        "template_generated": False,
                        "error": str(template_error)
                    }

        # 清理临时文件
        if temp_file_path and os.path.exists(temp_file_path):
            os.remove(temp_file_path)

        # 更新任务状态为完成
        task_storage.update_task_status(task_id, TaskStatus.COMPLETED)
        task_storage.update_task_progress(task_id, 100, "多模型预测完成")

        # 保存结果
        task_storage.set_task_result(task_id, final_result)

        logging.info(f"任务 {task_id}: 多模型预测完成，成功: {len(successful_results)}/{total_models}")

    except Exception as e:
        logging.error(f"任务 {task_id}: 多模型预测失败: {e}")
        task_storage.update_task_status(task_id, TaskStatus.FAILED, error=str(e))

def _predict_single_model(task_id, df, model_path, params_path, scaler_path,
                         selected_prot, selected_datatype, output_folder, csv_filename, device, auto_generate_template, save_result_file, current_user):
    """执行单个模型预测的函数 - 使用与主预测函数完全相同的逻辑"""
    import time
    import psutil

    try:
        # 开始时间和资源监控
        start_time = time.time()
        process = psutil.Process()
        cpu_cores = psutil.cpu_count()

        # 检查任务是否被取消
        task_info = task_storage.get_task(task_id)
        if task_info and task_info["status"] == TaskStatus.CANCELLED:
            return None

        # 更新进度
        task_storage.update_task_progress(task_id, 40, "加载模型参数")

        # 加载模型参数，包括用于异常检测的阈值参数
        with open(params_path, 'r') as f:
            params = json.load(f)

        error_mean = params.get("anomaly_threshold_mean", 0)
        error_std = params.get("anomaly_threshold_std", 0)
        k_sigma = 3.0

        # 初始化模型
        model = GRUModel(
            input_size=params.get("input_size", 1),
            hidden_size=params["hidden_size"],
            num_layers=params["num_layers"],
            dropout=params['dropout']
        ).to(device)
        model.load_state_dict(torch.load(model_path, map_location=device, weights_only=True))
        model.eval()

        # 加载Scaler
        scaler_y = joblib.load(scaler_path)
        sequence_length = params['sequence_length']

        # 更新进度
        task_storage.update_task_progress(task_id, 50, "预处理数据")

        # 使用与主预测函数完全相同的数据预处理逻辑
        filter_config = {
            "TCP": {
                "spt_sip_dip": {'groupby_keys': ['srcaddress', 'dstaddress', 'srcport']},
                "dpt_sip_dip": {'groupby_keys': ['srcaddress', 'dstaddress', 'dstport']},
                "len_dpt_syn": {'groupby_keys': ['dstaddress', 'dstport'], 'pre_filter': lambda d: d[d['tcpflags'] == 2]},
                "seq_ack_dip": {'groupby_keys': ['dstaddress', 'dstport'], 'pre_filter': lambda d: d[d['tcpflags'] == 16]}
            },
            "UDP": {
                "spt_sip_dip": {'groupby_keys': ['srcaddress', 'dstaddress', 'srcport']},
                "dpt_sip_dip": {'groupby_keys': ['srcaddress', 'dstaddress', 'dstport']}
            },
            "ICMP": {
                "dip": {'groupby_keys': ['dstaddress']}
            }
        }

        # 1. 根据协议筛选
        df_prot_filtered = df[df['protocol'] == selected_prot]
        if df_prot_filtered.empty:
            raise ValueError(f"在文件中找不到协议为 {selected_prot} 的数据。")

        # 2. 根据数据类型获取配置
        prot_config = filter_config.get(selected_prot, {})
        datatype_config = prot_config.get(selected_datatype)
        if not datatype_config:
            raise ValueError(f"无效的协议和数据类型组合: {selected_prot} - {selected_datatype}")

        # 3. 应用数据类型的特定过滤器
        df_data = df_prot_filtered
        if 'pre_filter' in datatype_config:
            if 'tcpflags' not in df_data.columns:
                 raise ValueError(f"数据缺少 'tcpflags' 列，无法应用 {selected_datatype} 筛选器。")
            df_data = datatype_config['pre_filter'](df_data)

        groupby_keys = datatype_config['groupby_keys']
        for key in groupby_keys:
            if key not in df_data.columns:
                raise ValueError(f"数据缺少 '{key}' 列，无法进行分组。")

        if not df_data.empty:
            # 修改逻辑：只保留记录数最多的组
            # 计算每个分组的大小
            group_counts = df_data.groupby(groupby_keys).size()

            # 找到记录数最多的组
            max_count = group_counts.max()
            largest_groups = group_counts[group_counts == max_count].index

            # 如果有多个组都是最大记录数，选择第一个
            if len(largest_groups) > 1:
                largest_group = largest_groups[0]
                logging.info(f"任务 {task_id}: 预测 {selected_prot} - {selected_datatype} 发现 {len(largest_groups)} 个最大组（记录数={max_count}），选择第一个: {largest_group}")
            else:
                largest_group = largest_groups[0]
                logging.info(f"任务 {task_id}: 预测 {selected_prot} - {selected_datatype} 选择最大组（记录数={max_count}）: {largest_group}")

            # 只保留最大组的数据
            if isinstance(largest_group, tuple):
                # 多列分组的情况
                mask = True
                for i, key in enumerate(groupby_keys):
                    mask = mask & (df_data[key] == largest_group[i])
                df_data = df_data[mask]
            else:
                # 单列分组的情况
                df_data = df_data[df_data[groupby_keys[0]] == largest_group]

        if df_data.empty:
            raise ValueError(f"根据选择的协议 ({selected_prot}) 和数据类型 ({selected_datatype}) 筛选后，没有剩余数据可用于预测。")

        df = df_data.copy()

        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s')
        df = df.sort_values('timestamp')

        resampled = df.resample('120s', on='timestamp').agg(
            total_packetssam=('packetssam', 'sum'),
            count=('packetssam', 'count')
        ) # 添加count列以支持智能插值
        resampled['packets_per_sec'] = resampled['total_packetssam'] / 960

        # 使用智能插值策略，区分真实零值和缺失值
        resampled = optimized_interpolation_pipeline(resampled, task_id)

        smoothing_window = 5
        nf_data = resampled.reset_index().copy()
        nf_data['packets_per_sec_smooth'] = nf_data['packets_per_sec'].rolling(window=smoothing_window, min_periods=1).mean()

        nf_data['packets_per_sec_log'] = np.log1p(nf_data['packets_per_sec_smooth'])
        nf_data['packets_per_sec_log_diff'] = nf_data['packets_per_sec_log'].diff().fillna(0)

        target_col = 'packets_per_sec_log_diff'

        if len(nf_data) < sequence_length:
             raise ValueError("数据不足，无法生成任何预测序列")

        X_seq, _ = create_sequences(nf_data[[target_col]].values, nf_data[target_col].values, sequence_length)

        if X_seq.shape[0] == 0:
            raise ValueError("数据处理后不足以生成预测序列")

        # 更新进度
        task_storage.update_task_progress(task_id, 70, "执行预测")

        # 修改：将 NumPy 数组转换为 DataFrame 并设置列名，以匹配训练时的格式
        X_seq_df = pd.DataFrame(X_seq.reshape(-1, 1), columns=['packets_per_sec_log_diff'])
        X_seq_scaled = scaler_y.transform(X_seq_df).reshape(X_seq.shape)
        X_tensor = torch.tensor(X_seq_scaled, dtype=torch.float32).to(device)

        # 模型预测与逆转换
        with torch.no_grad():
            predictions_scaled = model(X_tensor).cpu().numpy().flatten()

        # 修改：将预测结果转换为 DataFrame 并设置列名，以匹配训练时的格式
        predictions_scaled_df = pd.DataFrame(predictions_scaled.reshape(-1, 1), columns=['packets_per_sec_log_diff'])
        predictions_diff = scaler_y.inverse_transform(predictions_scaled_df).flatten()

        last_log_values_start_index = len(nf_data) - len(predictions_diff) - 1
        last_log_values = nf_data['packets_per_sec_log'].iloc[last_log_values_start_index:-1].values

        min_len = min(len(predictions_diff), len(last_log_values))
        predictions_diff = predictions_diff[:min_len]
        last_log_values = last_log_values[:min_len]
        predicted_log_values = last_log_values + predictions_diff

        predicted_smooth_values = np.expm1(predicted_log_values)

        # 计算动态阈值并准备输出
        dynamic_thresholds = predicted_smooth_values + error_mean + k_sigma * error_std

        # 计算一个总的建议阈值
        if len(predicted_smooth_values) > 0:
            suggested_threshold = float(np.mean(predicted_smooth_values) + error_mean + k_sigma * error_std)
        else:
            suggested_threshold = float(error_mean + k_sigma * error_std)

        # 更新进度
        task_storage.update_task_progress(task_id, 90, "保存预测结果")

        output_start_index = len(nf_data) - len(predicted_smooth_values)
        pred_df = nf_data.iloc[output_start_index:].copy()

        pred_df['pred_smooth'] = predicted_smooth_values
        pred_df['threshold'] = dynamic_thresholds
        # 判断每一行是否为异常
        pred_df['is_anomaly'] = pred_df['packets_per_sec_smooth'] > pred_df['threshold']

        # 将数值结果四舍五入为整数，并确保是 Python 原生类型
        pred_df['packets_per_sec'] = pred_df['packets_per_sec'].round().astype('int64').astype(int)
        pred_df['packets_per_sec_smooth'] = pred_df['packets_per_sec_smooth'].round().astype('int64').astype(int)
        pred_df['pred_smooth'] = pred_df['pred_smooth'].round().astype('int64').astype(int)
        pred_df['threshold'] = pred_df['threshold'].round().astype('int64').astype(int)

        # 确保布尔值是 Python 原生类型
        pred_df['is_anomaly'] = pred_df['is_anomaly'].astype(bool)

        # 将数值结果四舍五入为整数
        pred_df['packets_per_sec'] = pred_df['packets_per_sec'].round().astype(int)
        pred_df['packets_per_sec_smooth'] = pred_df['packets_per_sec_smooth'].round().astype(int)
        pred_df['pred_smooth'] = pred_df['pred_smooth'].round().astype(int)
        pred_df['threshold'] = pred_df['threshold'].round().astype(int)

        # 将 timestamp 转换为字符串以便 JSON 序列化
        pred_df['timestamp'] = pred_df['timestamp'].dt.strftime('%Y-%m-%d %H:%M:%S')

        # 计算详细预测数据和异常数量（用于阈值计算，但异步预测不返回详细数据）
        output_data = pred_df[['timestamp', 'packets_per_sec', 'packets_per_sec_smooth', 'pred_smooth', 'threshold', 'is_anomaly']].to_dict('records')
        anomaly_count = int(pred_df['is_anomaly'].sum())

        model_name_display = os.path.basename(model_path).replace('_model_best.pth', '')

        # 更新进度
        task_storage.update_task_progress(task_id, 100, "预测完成")

        # 资源监控结束
        duration = time.time() - start_time
        cpu_usage = process.cpu_percent(interval=None)
        cpu_usage_normalized = cpu_usage / cpu_cores if cpu_cores else cpu_usage
        memory_mb = process.memory_info().rss / (1024 * 1024)

        # GPU资源监控
        gpu_memory_mb = 0
        gpu_utilization_percent = 0
        try:
            import pynvml
            if device.type == 'cuda':
                pynvml.nvmlInit()
                handle = pynvml.nvmlDeviceGetHandleByIndex(0)
                mem_info = pynvml.nvmlDeviceGetMemoryInfo(handle)
                util_rates = pynvml.nvmlDeviceGetUtilizationRates(handle)
                gpu_memory_mb = mem_info.used / (1024 * 1024)
                gpu_utilization_percent = util_rates.gpu
                pynvml.nvmlShutdown()
        except:
            pass  # 如果GPU监控失败，则静默处理

        # 保存预测结果到文件（用于生成清洗模板）
        result_path = None
        if save_result_file:
            try:
                task_storage.update_task_progress(task_id, 85, "保存预测结果")

                # 构建结果文件路径 - 使用日期而不是时间戳，便于多模型共享同一文件
                from datetime import datetime
                date_str = datetime.now().strftime("%Y%m%d")
                result_filename = f"{csv_filename}_{date_str}_results.txt"
                result_path = os.path.join(output_folder, result_filename)

                # 确保输出目录存在
                os.makedirs(output_folder, exist_ok=True)

                # 将阈值写入结果文件
                threshold_to_write = int(np.round(suggested_threshold))
                result_line = f"{selected_prot} {selected_datatype} {threshold_to_write}\n"

                # 检查是否已存在结果文件，如果存在则追加而不是覆盖
                if os.path.exists(result_path):
                    # 读取现有内容
                    with open(result_path, 'r', encoding='utf-8') as f:
                        existing_lines = f.readlines()

                    # 检查是否已存在相同的协议和数据类型组合
                    updated = False
                    for i, line in enumerate(existing_lines):
                        parts = line.strip().split()
                        if len(parts) >= 2 and parts[0] == selected_prot and parts[1] == selected_datatype:
                            existing_lines[i] = result_line
                            updated = True
                            break

                    if not updated:
                        existing_lines.append(result_line)

                    # 写回文件
                    with open(result_path, 'w', encoding='utf-8') as f:
                        f.writelines(existing_lines)
                else:
                    # 创建新文件
                    with open(result_path, 'w', encoding='utf-8') as f:
                        f.write(result_line)

                logging.info(f"任务 {task_id}: 预测结果已保存到: {result_path}")

            except Exception as e:
                logging.warning(f"任务 {task_id}: 保存预测结果文件失败: {e}")
                result_path = None

        # 自动生成清洗模板（如果用户选择了该选项）
        template_info = None
        if auto_generate_template:
            # 如果没有保存结果文件，需要先找到或创建结果文件
            if not result_path:
                from datetime import datetime
                date_str = datetime.now().strftime("%Y%m%d")
                result_filename = f"{csv_filename}_{date_str}_results.txt"
                result_path = os.path.join(output_folder, result_filename)

            if result_path and os.path.exists(result_path):
                try:
                    task_storage.update_task_progress(task_id, 90, "生成清洗模板")

                    from .clean_template import generate_clean_template
                    import asyncio

                    # 在同步函数中调用异步函数
                    try:
                        loop = asyncio.get_event_loop()
                        template_result = loop.run_until_complete(generate_clean_template(
                            results_file=result_path,
                            output_folder=output_folder,
                            template_name=None,  # 使用默认命名规则
                            current_user=current_user
                        ))
                    except RuntimeError:
                        # 如果没有事件循环，创建一个新的
                        template_result = asyncio.run(generate_clean_template(
                            results_file=result_path,
                            output_folder=output_folder,
                            template_name=None,  # 使用默认命名规则
                            current_user=current_user
                        ))

                    template_info = {
                        "template_generated": True,
                        "template_path": template_result.get("template_path"),
                        "updated_thresholds": template_result.get("updated_thresholds")
                    }
                    logging.info(f"任务 {task_id}: 自动生成清洗模板成功 - {template_result.get('template_path')}")

                except Exception as template_error:
                    logging.warning(f"任务 {task_id}: 自动生成清洗模板失败: {template_error}")
                    template_info = {
                        "template_generated": False,
                        "error": str(template_error)
                    }

        # 返回结果 - 异步预测优化：保留所有计算但只返回前端需要的字段
        result = {
            # 前端需要的核心字段
            "suggested_threshold": float(suggested_threshold),
            "model_name": str(model_name_display),
            "message": "预测成功",
            "duration_seconds": float(duration),
            "cpu_percent": float(cpu_usage_normalized),
            "memory_mb": float(memory_mb),
            "gpu_memory_mb": float(gpu_memory_mb),
            "gpu_utilization_percent": float(gpu_utilization_percent)
            # 注意：不返回 predictions 数组和 anomaly_count 以节省存储空间
            # 这些数据已经计算完成并用于阈值计算，但不需要存储到 JSON 中
        }

        if template_info:
            result["template_info"] = template_info
        if result_path:
            result["result_path"] = result_path

        return result

    except Exception as e:
        logging.error(f"任务 {task_id}: 预测时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"预测过程中发生严重错误: {str(e)}")